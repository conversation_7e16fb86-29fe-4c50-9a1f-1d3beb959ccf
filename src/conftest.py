import base64
import importlib
import inspect

from typing import (
    Callable,
    Tuple,
    Type,
)

from django.conf import settings
from django.contrib.auth.models import Permission
from django.contrib.messages.storage.fallback import FallbackStorage
from django.core.cache import cache
from django.db import connection
from django.test import RequestFactory

import inflection
import pytest

from factory import Factory
from factory.base import StubObject
from pytest_django.lazy_django import skip_if_no_django
from rest_framework.test import (
    APIClient,
    APIRequestFactory,
)
from user_agents.parsers import UserAgent

from carts.services.cart_service import CartService
from custom.enums import ShelfType
from custom.tests.utils.fake_nbp_exchange_table import fake_nbp_get_exchange_table
from gallery.serializers import (
    JettySerializerForProduction,
    SottySerializer,
    WattySerializer,
)
from invoice.choices import (
    InvoiceStatus,
    NumerationType,
)
from regions.constants import OTHER_REGION_NAME
from user_profile.choices import UserType
from vouchers.enums import VoucherType, ServiceType

_run_all_tests_report = []


FACTORIES_MODULE_PATHS: Tuple[str, ...] = (
    'abtests.tests.factories',
    'accounting.tests.factories',
    'automatic_batching.tests.factories',
    'b2b.tests.factories',
    'carts.tests.factories',
    'catalogue.tests.factories',
    'complaints.tests.factories',
    'custom.tests.factories',
    'custom_audiences.tests.factories',
    'customer_service.tests.factories',
    'dynamic_delivery.tests.factories',
    'events.tests.factories',
    'feeds.tests.factories',
    'free_returns.tests.factories',
    'gallery.tests.factories',
    'invoice.tests.factories',
    'loose_ends.tests.factories',
    'mailing.tests.factories',
    'material_recovery.tests.factories',
    'orders.tests.factories',
    'payments.tests.factories',
    'pricing_v3.tests.factories',
    'producers.tests.factories',
    'product_feeds.tests.factories',
    'production_margins.tests.factories',
    'promotions.tests.factories',
    'rating_tool.tests.factories',
    'regions.tests.factories',
    'render_tasks.tests.factories',
    'reviews.tests.factories',
    'shortener.tests.factories',
    'services.tests.factories',
    'showrooms.tests.factories',
    'user_consents.tests.factories',
    'user_profile.tests.factories',
    'vouchers.tests.factories',
    'waiting_list.tests.factories',
    'warehouse.tests.factories',
)


class FactoryFixtureRegistry:
    """
    Register factories as fixtures similar to achieve the same effect as
     `pytest-factoryboy` package but without bugs.
    """

    @classmethod
    def register(cls, fixtures_namespace):
        """Dynamically register fixtures for all factories"""
        for factories_module_path in FACTORIES_MODULE_PATHS:
            factories_module = importlib.import_module(factories_module_path)
            for name, factory_wannabe in factories_module.__dict__.items():
                if name.startswith('_'):
                    continue
                if (
                    inspect.isclass(factory_wannabe)
                    and issubclass(factory_wannabe, Factory)
                    and not factory_wannabe._meta.abstract
                ):
                    fixture_name = cls._get_model_name(factory_wannabe)
                    factory_fixture_name = f'{fixture_name}_factory'
                    factory_class = getattr(factories_module, name)

                    # Register the fixture in the provided namespace
                    fixtures_namespace[
                        factory_fixture_name
                    ] = cls._generate_factory_fixture(factory_class)
                    fixtures_namespace[
                        fixture_name
                    ] = cls._generate_factory_instance_fixture(factory_class)

    @staticmethod
    def _generate_factory_instance_fixture(factory: Type[Factory]) -> Callable:
        @pytest.fixture()
        def fixture():
            return factory()

        return fixture

    @staticmethod
    def _generate_factory_fixture(factory: Type[Factory]) -> Callable:
        @pytest.fixture()
        def fixture():
            return factory

        return fixture

    @staticmethod
    def _get_model_name(factory_class: Type[Factory]) -> str:
        model_name = factory_class.__name__.replace('Factory', '')
        return inflection.underscore(model_name)


def pytest_addoption(parser):
    parser.addoption(
        '--run-all-tests',
        action='store_true',
        dest='run_all_tests',
        default=False,
        help='Run all tests. Simply force `-m=""` cli option.',
    )


def pytest_configure(config):
    FactoryFixtureRegistry.register(vars(config))


def pytest_sessionstart(session):
    if session.config.getoption('run_all_tests', False):
        _override_config_option(session.config, 'markexpr', None)
        _run_all_tests_report.append(
            'WARNING: `markexpr` (read from cli `-m`) option ignored',
        )


def pytest_report_collectionfinish(config, startdir, items):
    return [*_run_all_tests_report]


def _override_config_option(config, name, new_value):
    name = config._opt2dest.get(name, name)
    setattr(config.option, name, new_value)


@pytest.fixture()
def guest_user(user_factory):
    return user_factory(profile__user_type=UserType.GUEST_CUSTOMER)


@pytest.fixture
def api_rf():
    """rest_framework ``APIRequestFactory`` instance."""
    skip_if_no_django()
    return APIRequestFactory()


@pytest.fixture()
def admin_user(admin_user, region):
    admin_user.profile.region = region
    admin_user.profile.save()
    return admin_user


@pytest.fixture()
def user(user, region):
    user.profile.region = region
    user.profile.save()
    return user


@pytest.fixture(autouse=True)
def reset_cache():
    """Workaround when django doesn't flushes caches between tests."""
    yield
    cache.clear()


@pytest.fixture()
def global_strikethrough_promo_30(
    voucher_factory,
    promotion_factory,
    promotion_config_factory,
):
    voucher = voucher_factory(value=30, is_percentage=True)
    promo = promotion_factory(
        strikethrough_pricing=True,
        active=True,
        promo_code=voucher,
    )
    # this forces adding all existing regions to `enabled_regions`
    promotion_config_factory(promotion=promo)
    return promo


@pytest.fixture
def api_client():
    """rest_framework tests ``APIClient`` instance."""
    skip_if_no_django()
    client = APIClient()

    # save session to avoid additional db hits in ProcessAnonymousMiddleware
    client.session.save()
    return client


@pytest.fixture
def braze_client(api_client, user):
    api_client.force_authenticate(user)
    api_client.credentials(
        HTTP_BRAZE_APP_ID=settings.BRAZE_APP_ID,
        HTTP_BRAZE_API_KEY=settings.BRAZE_API_KEY,
    )
    return api_client


@pytest.fixture(autouse=True)
def _nbp_marker(request, mocker):
    """Implementation of ``nbp`` pytest's marker.

    Mock ``get_exchange_table`` when ``mock_requests`` marker argument truthy.

    """
    marker = request.node.get_closest_marker('nbp')
    if marker:
        mock_requests = _validate_nbp_marker(marker)
        if mock_requests:
            mocker.patch(
                'custom.api_clients.nbp.NBPAPIClient.get',
                side_effect=fake_nbp_get_exchange_table,
            )


@pytest.fixture(autouse=True)
def mock_google_headless_get_pdf(mocker):
    response = base64.b64decode('')
    mocker.patch(
        'custom.google_headless.ChromeHeadless.get_pdf_from_template',
        return_value=response,
    )


def _validate_nbp_marker(marker):
    def attach_attributes(mock_requests=True):
        return mock_requests

    return attach_attributes(*marker.args, **marker.kwargs)


@pytest.fixture()
def cape_dnas(custom_dna_factory):
    custom_dna_factory(dna_objects=[1])
    custom_dna_factory(dna_objects=[2, 3])
    custom_dna_factory(dna_objects=[4, 5])


@pytest.fixture()
def old_dnas(custom_dna_factory):
    custom_dna_factory(
        shelf_type=0,
        configurator_type=1,
        pattern_slot=0,
    )
    custom_dna_factory(
        shelf_type=0,
        configurator_type=1,
        pattern_slot=1,
    )
    custom_dna_factory(
        shelf_type=0,
        configurator_type=1,
        pattern_slot=2,
    )
    custom_dna_factory(
        shelf_type=0,
        configurator_type=1,
        pattern_slot=3,
    )


@pytest.fixture()
def regions(
    region_factory,
    country_factory,
    invoice_sequence_factory,
    currency_factory,
):
    zloty = currency_factory(code='PLN', symbol='zł', name='Złoty')
    euro = currency_factory(euro=True)

    region_factory(
        currency=euro,
        name=OTHER_REGION_NAME,
        default_for_language=None,
    )
    poland_region = region_factory(name='poland', currency=zloty)
    austria_region = region_factory(name='austria', currency=euro)
    belgium_region = region_factory(name='belgium', currency=euro)
    germany_region = region_factory(name='germany', currency=euro)

    poland = country_factory(
        name='poland',
        region=poland_region,
        locale='pl_PL',
        code='PL',
    )
    austria = country_factory(
        name='austria',
        region=austria_region,
        locale='de_AT',
        code='AT',
    )
    belgium = country_factory(
        name='belgium',
        region=belgium_region,
        locale='nl_BE',
        code='BE',
    )
    germany = country_factory(
        name='germany',
        region=germany_region,
        locale='de_DE',
        code='DE',
    )

    for country in (poland, austria, belgium, germany):
        for invoice_type in (
            InvoiceStatus.ENABLED,
            InvoiceStatus.CORRECTING,
        ):
            invoice_sequence_factory(
                country=country,
                numeration_type=NumerationType.NORMAL,
                invoice_type=invoice_type,
            )
    return [
        poland_region,
        austria_region,
        belgium_region,
        germany_region,
    ]


@pytest.fixture()
def wisely_user(db, user_factory):
    wisely_permission, _ = Permission.objects.get_or_create(codename='wisely_general')
    user = user_factory.create(
        username='wisely',
    )
    user.user_permissions.add(wisely_permission.id)
    return user


@pytest.fixture()
def wisely_api_client(wisely_user):
    api_client = APIClient()
    api_client.force_authenticate(wisely_user)
    return api_client


@pytest.fixture(scope='session')
def django_db_setup(django_db_setup, django_db_use_migrations, django_db_blocker):
    if not django_db_use_migrations:
        with django_db_blocker.unblock():
            with connection.cursor() as c:
                c.execute('CREATE EXTENSION IF NOT EXISTS unaccent')


class DictFactory:
    """
    Used to prepare data from FactoryBoy without saving it to the database.
    Can be used i.e. to prepare data to be sent by POST.
    Usage example:
        furniture_data = DictFactory(jetty_factory).build()
    """

    def __init__(self, factory_class):
        self.factory_class = factory_class

    def stub_to_dict(self, stub):
        stub_dict = stub.__dict__
        for key, value in stub_dict.items():
            if isinstance(value, StubObject):
                stub_dict[key] = self.stub_to_dict(value)
        return stub_dict

    def build(self, **kwargs):
        stub = self.factory_class.stub(**kwargs)
        stub_dict = self.stub_to_dict(stub)
        return stub_dict


@pytest.fixture
@pytest.mark.django_db
def admin_action_request(user_factory):
    request = RequestFactory()
    request.user = user_factory()
    request.user_agent = UserAgent('user agent')
    request.GET = {}
    request.COOKIES = {}
    request.META = {
        'HTTP_HOST': 'localhost:8000',
        'HTTP_ORIGIN': 'localhost:8000',
        'PATH_INFO': 'admin',
    }
    setattr(request, 'session', 'session')
    messages = FallbackStorage(request)
    setattr(request, '_messages', messages)
    return request


@pytest.fixture()
def user_admin(db, user_factory):
    return user_factory(is_admin=True)


@pytest.fixture(autouse=True)
def media_storage(settings, tmpdir):
    settings.MEDIA_ROOT = tmpdir.strpath


@pytest.fixture(autouse=True)
def mock_get_cstm_token(mocker):
    mocker.patch('custom.internal_api.clients.get_cstm_token', return_value='token')


@pytest.fixture
def jetty_data(jetty_factory):
    return JettySerializerForProduction(instance=jetty_factory(preview=None)).data


@pytest.fixture
def sotty_data(sotty_factory):
    return SottySerializer(instance=sotty_factory(preview=None)).data


@pytest.fixture
def watty_data(watty_factory):
    data = WattySerializer(watty_factory(preview=None)).data
    data['configurator_params']['geom_id'] = 1
    return data


@pytest.fixture(autouse=True)
def braze_credentials(settings):
    settings.BRAZE_API_KEY = 'api_key'
    settings.BRAZE_APP_ID = 'app_id'


@pytest.fixture
def assembly_region_keys(settings):
    settings.ASSEMBLY_REGION_KEYS = [
        'germany',
        'belgium',
        'netherlands',
        'luxembourg',
        'austria',
        'france',
        'switzerland',
        'united_kingdom',
        'denmark',
        'poland',
    ]


@pytest.fixture(autouse=True)
def euro_currency(db, currency_factory, currency_rate_factory):
    currency = currency_factory(code='EUR', name='Euro', symbol='€')
    currency_rate_factory(currency=currency, rate=1.0)
    return currency


@pytest.fixture
def neutral_currency(currency_factory, currency_rate_factory):
    currency = currency_factory(rates=[])
    currency_rate_factory(currency=currency, rate=1.0)
    return currency


@pytest.fixture
def rates_neutral_region(region_factory, neutral_currency, region_rate_factory):
    neutral_region = region_factory(currency=neutral_currency, is_eu=True)
    region_rate_factory(region=neutral_region, rate=1)
    return neutral_region


@pytest.fixture()
def region_de(region_factory, euro_currency):
    return region_factory(
        name='germany',
        currency=euro_currency,
        default_for_language='tde',
    )


@pytest.fixture()
def region_pl(region_factory, currency_factory):
    return region_factory(
        name='poland',
        currency=currency_factory(code='PLN', symbol='zł', name='Złoty'),
        default_for_language='pl',
    )


@pytest.fixture()
def region_uk(region_factory, euro_currency):
    return region_factory(
        name='united_kingdom',
        currency=euro_currency,
        default_for_language='tuk',
    )


@pytest.fixture()
def region_fr(region_factory, euro_currency):
    return region_factory(
        name='france',
        currency=euro_currency,
        default_for_language='fr',
    )


@pytest.fixture()
def region_it(region_factory, euro_currency):
    return region_factory(
        name='italy',
        currency=euro_currency,
        default_for_language='it',
    )


@pytest.fixture()
def region_no(region_factory, euro_currency):
    return region_factory(
        name='norway',
        currency=euro_currency,
        default_for_language='no',
    )


@pytest.fixture()
def region_dk(region_factory, euro_currency):
    return region_factory(
        name='denmark',
        currency=euro_currency,
        default_for_language='dk',
    )


@pytest.fixture()
def region_es(region_factory, euro_currency):
    return region_factory(
        name='spain',
        currency=euro_currency,
        default_for_language='es',
    )


@pytest.fixture()
def region_pt(region_factory, euro_currency):
    return region_factory(
        name='portugal',
        currency=euro_currency,
        default_for_language='pt',
    )


@pytest.fixture()
def region_gr(region_factory, euro_currency):
    return region_factory(
        name='greece',
        currency=euro_currency,
        default_for_language='en',
    )


@pytest.fixture()
def region_fi(region_factory, euro_currency):
    return region_factory(
        name='finland',
        currency=euro_currency,
        default_for_language='en',
    )


@pytest.fixture(autouse=True)
def region_other(region_factory, euro_currency):
    return region_factory(
        name=OTHER_REGION_NAME,
        currency=euro_currency,
        default_for_language=None,
    )


@pytest.fixture
def cart_with_sofa(cart_factory, cart_item_factory, region_de):
    cart = cart_factory(region=region_de, items=[])
    cart_item_factory(cart=cart, is_sofa_wool=True)
    return cart


@pytest.fixture
def cart_with_sofa_and_old_sofa_collection(
    cart_with_sofa,
):
    service = CartService(cart_with_sofa)
    service.change_old_sofa_collection(activate=True)

    return cart_with_sofa



@pytest.fixture
def delivery_voucher(voucher_factory, item_discount_factory):
    voucher = voucher_factory(value=20, kind_of=VoucherType.PERCENTAGE)
    item_discount_delivery = item_discount_factory(
        value=50,
        kind_of=VoucherType.PERCENTAGE,
        service_type=ServiceType.DELIVERY,
    )
    item_discount_sotty = item_discount_factory(
        value=0,
        kind_of=VoucherType.PERCENTAGE,
        shelf_type=ShelfType.SOFA_TYPE01,
    )
    voucher.discounts.add(item_discount_delivery, item_discount_sotty)
    return voucher

