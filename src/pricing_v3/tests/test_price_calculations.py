from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from unittest import mock
from unittest.mock import patch

from django.urls import reverse
from django.utils import timezone

import pytest

from pytest_cases import parametrize_with_cases

from carts.models import CartItem
from carts.services.cart_service import CartService
from custom.constants import (
    VAT_EU,
    VAT_EXPORT,
    VAT_NORMAL,
)
from custom.enums import ShelfType
from orders.choices import VatType
from orders.enums import OrderStatus
from orders.models import OrderItem
from pricing_v3.services.item_price_calculators import (
    CartItemPriceCalculator,
    OrderItemPriceCalculator,
)
from pricing_v3.services.price_calculators import (
    CartPriceCalculator,
    OrderPriceCalculator,
)
from regions.mixins import RegionCalculationsObject
from vouchers.enums import VoucherType
from vouchers.services.voucher_validator import VoucherService


@pytest.fixture
def region_germany(region_factory, currency_rate_factory, region_rate_factory):
    region = region_factory(germany=True)
    currency_rate_factory(currency=region.currency, rate=1.5)
    region_rate_factory(region=region, rate=2)
    return region


@pytest.fixture()
def region_germany_neutral(
    region_factory,
    currency_rate_factory,
    region_rate_factory,
):
    region = region_factory(germany=True, currency__rates=[])
    currency_rate_factory(currency=region.currency, rate=1.0)
    region_rate_factory(region=region, rate=1)
    return region


@pytest.mark.django_db
class TestVoucherPriceCalculation:
    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_handle_promo_sets_correct_values(
        self,
        factory_name,
        price_calculator,
        request,
        voucher_factory,
    ):
        instance = request.getfixturevalue(factory_name)()
        price_calculator(instance).calculate()

        voucher = voucher_factory(is_absolute=True)
        voucher.create_region_entries()
        instance.promo_text = voucher.code
        price_calculator(instance)._handle_promo(
            ignore_vouchers_check=False, change_promo_quantity=True
        )

        assert (
            instance.region_promo_amount
            == voucher.region_entries.get(region=instance.region).value
        )

    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_price_calculator_with_working_promocode_sets_correct_values(
        self,
        factory_name,
        price_calculator,
        voucher_factory,
        voucher_group_factory,
        request,
    ):
        instance = request.getfixturevalue(factory_name)()
        price_calculator(instance).calculate()

        voucher_group = voucher_group_factory(region=instance.region)
        voucher = voucher_factory(
            value=50,
            is_absolute=True,
            group=voucher_group,
            amount_starts=200,
        )
        total_price_before_adjust = instance.total_price
        region_total_price_before_adjust = instance.region_total_price
        instance.promo_text = voucher.code
        price_calculator(instance).calculate()

        assert instance.total_price == total_price_before_adjust - instance.promo_amount
        assert instance.region_total_price == (
            region_total_price_before_adjust - instance.region_promo_amount
        )

    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_price_calculator_clears_promo_for_empty_string(
        self,
        factory_name,
        price_calculator,
        request,
        voucher_factory,
    ):
        voucher = voucher_factory(is_absolute=True)
        instance = request.getfixturevalue(factory_name)(used_promo=voucher)
        instance.promo_text = ''
        calculator = price_calculator(instance)
        calculator.calculate()
        assert calculator.message_status == 'ok'
        assert not instance.used_promo

    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_price_calculator_returns_ok_for_correct_promo_code(
        self,
        factory_name,
        price_calculator,
        request,
        voucher_factory,
        voucher_group_factory,
        region_factory,
    ):
        region = region_factory(germany=True)
        voucher_group = voucher_group_factory(region=region)
        voucher = voucher_factory(
            is_absolute=True,
            group=voucher_group,
            amount_starts=200,
        )
        instance = request.getfixturevalue(factory_name)(
            used_promo=voucher,
            region=region,
        )
        instance.promo_text = voucher.code
        calculator = price_calculator(instance)
        calculator.calculate()
        assert calculator.message_status == 'ok'
        assert instance.used_promo == voucher

    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    @pytest.mark.parametrize('voucher_value', (20, 50))
    def test_check_promo_for_percentage_voucher(
        self,
        factory_name,
        price_calculator,
        request,
        voucher_value,
        voucher_factory,
        voucher_group_factory,
        region_factory,
    ):
        region = region_factory(germany=True)
        voucher_group = voucher_group_factory(region=region)
        voucher = voucher_factory(
            value=voucher_value,
            is_percentage=True,
            group=voucher_group,
            amount_starts=10,
        )
        instance = request.getfixturevalue(factory_name)(
            used_promo=voucher,
            region=region,
        )
        price_calculator(instance).calculate()
        price_before_discount = instance.region_total_price
        instance.promo_text = voucher.code
        calculator = price_calculator(instance)
        calculator.calculate()
        discount_amount = price_before_discount * voucher_value / 100
        assert calculator.message_status == 'ok'
        assert (
            instance.region_total_price
            == price_before_discount - discount_amount.quantize(Decimal('1'))
        )

    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_price_calculator_sets_proper_values(
        self,
        factory_name,
        price_calculator,
        request,
        voucher_factory,
        voucher_group_factory,
        region_factory,
        currency_rate_factory,
        region_rate_factory,
    ):
        region = region_factory(germany=True)
        currency_rate_factory(currency=region.currency, rate=1)
        region_rate_factory(region=region, rate=1)

        voucher_group = voucher_group_factory(region=region)
        voucher = voucher_factory(
            is_absolute=True, group=voucher_group, amount_starts=0, value=200
        )
        instance = request.getfixturevalue(factory_name)(
            used_promo=voucher,
            region=region,
        )
        instance.promo_text = voucher.code
        price_calculator(instance).calculate()
        instance.refresh_from_db()
        assert instance.region_promo_amount == 200
        assert instance.region_promo_amount_net == Decimal('162.60')
        assert instance.promo_amount == 200
        assert instance.promo_amount_net == Decimal('162.60')

    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_price_calculator_changes_promo_quantity_if_it_should(
        self,
        factory_name,
        price_calculator,
        request,
        voucher_factory,
    ):
        voucher = voucher_factory(quantity=10, quantity_left=10)
        instance = request.getfixturevalue(factory_name)(promo_text=voucher.code)
        price_calculator(instance).calculate(change_promo_quantity=True)
        voucher.refresh_from_db()
        assert voucher.quantity_left == 9

    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_voucher_cannot_apply_one_time_voucher_twice(
        self,
        factory_name,
        price_calculator,
        request,
        voucher_factory,
    ):
        voucher = voucher_factory(quantity=1, quantity_left=1)
        instance1 = request.getfixturevalue(factory_name)(promo_text=voucher.code)
        instance2 = request.getfixturevalue(factory_name)(promo_text=voucher.code)
        if factory_name == 'order_factory':
            instance1.status = OrderStatus.CART
            instance2.status = OrderStatus.CART

        price_calculator(instance1).calculate(change_promo_quantity=True)
        voucher.refresh_from_db()
        price_calculator(instance2).calculate(change_promo_quantity=True)
        assert instance2.promo_text == ''

    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_price_calculator_promo_for_b2b(
        self,
        factory_name,
        price_calculator,
        request,
        mocker,
        country_factory,
        region_germany_neutral,
        voucher_factory,
    ):
        instance = request.getfixturevalue(factory_name)(region=region_germany_neutral)
        instance.region.country = country_factory(name='germany', vat=Decimal('0.33'))
        instance.vat = 'DE262231084'  # approved test vat number

        mocker.patch(
            'gallery.models.Jetty.get_shelf_price_as_number',
            return_value=500,
        )
        voucher = voucher_factory(value=50, is_absolute=True)
        instance.promo_text = voucher.code
        price_calculator(instance).calculate(check_vat=True)

        assert instance.vat_type == VAT_EU
        assert instance.region_total_price == Decimal('701.88')  # 1000 / 1.33 - 50
        assert instance.region_total_price_net == Decimal('701.88')
        assert instance.region_vat_amount == Decimal('0')

    @pytest.mark.parametrize(
        ['factory_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_price_calculator_promo_for_b2b_but_invalid_vat_number(
        self,
        factory_name,
        price_calculator,
        request,
        mocker,
        country_factory,
        region_germany_neutral,
        voucher_factory,
    ):
        instance = request.getfixturevalue(factory_name)(region=region_germany_neutral)
        instance.region.country = country_factory(name='germany', vat=Decimal('0.33'))
        instance.vat = 'DE123456789'

        mocker.patch(
            'gallery.models.Jetty.get_shelf_price_as_number',
            return_value=500,
        )
        voucher = voucher_factory(value=50, is_absolute=True)
        instance.promo_text = voucher.code
        price_calculator(instance).calculate(check_vat=True)

        assert instance.vat_type == VAT_NORMAL
        assert instance.region_total_price == Decimal('950')
        assert instance.region_total_price_net == Decimal(
            '772.36'
        )  # (1000 - 50) / 1.23
        assert instance.region_vat_amount == Decimal('177.64')


@pytest.fixture
def region_united_kingdom(
    region_factory,
    currency_rate_factory,
    region_rate_factory,
):
    region = region_factory(united_kingdom=True, currency__rates=[])
    currency_rate_factory(currency=region.currency, rate=1.5)
    region_rate_factory(region=region, rate=2)
    return region


@pytest.mark.django_db
class TestItemPriceCalculatorForB2CInsideEU:
    @pytest.fixture
    def order_item_eu_b2c(self, order_item_factory, region_germany):
        return order_item_factory(
            order__region=region_germany,
            order__vat_type=VatType.LOCAL_VAT.legacy_type,
            is_jetty=True,
        )

    @pytest.fixture
    def cart_item_eu_b2c(self, cart_item_factory, region_germany):
        return cart_item_factory(
            cart__region=region_germany,
            cart__vat_type=VatType.LOCAL_VAT.legacy_type,
            is_jetty=True,
        )

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2c', OrderItemPriceCalculator],
            ['cart_item_eu_b2c', CartItemPriceCalculator],
        ],
    )
    def test_calculate_region_price(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        """Multiply shelf price by region and currency rate."""
        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.region_price == Decimal('333')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2c', OrderItemPriceCalculator],
            ['cart_item_eu_b2c', CartItemPriceCalculator],
        ],
    )
    def test_calculate_region_price_net(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        """Multiply shelf price by region and currency rate and subtract vat rate."""
        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.region_price_net == Decimal('234.51')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2c', OrderItemPriceCalculator],
            ['cart_item_eu_b2c', CartItemPriceCalculator],
        ],
    )
    def test_calculate_income_price(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        """Divide region price by currency rate."""
        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.price == Decimal('222')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2c', OrderItemPriceCalculator],
            ['cart_item_eu_b2c', CartItemPriceCalculator],
        ],
    )
    def test_calculate_income_price_net(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        """Divide region price by currency rate and subtract vat rate."""
        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.price_net == Decimal('156.34')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2c', OrderItemPriceCalculator],
            ['cart_item_eu_b2c', CartItemPriceCalculator],
        ],
    )
    def test_calculate_region_vat_amount(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.region_vat_amount == Decimal('98.49')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2c', OrderItemPriceCalculator],
            ['cart_item_eu_b2c', CartItemPriceCalculator],
        ],
    )
    def test_calculate_income_vat_amount(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.vat_amount == Decimal('65.66')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2c', OrderItemPriceCalculator],
            ['cart_item_eu_b2c', CartItemPriceCalculator],
        ],
    )
    def test_calculate_assembly_price(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)
        mocker.patch('gallery.models.Jetty.get_assembly_price', return_value=42)
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.assembly_price == Decimal('84')
        # assemby price * region rate * currency rate
        assert calculated_item.region_assembly_price == Decimal('126')


@pytest.mark.django_db
class TestItemPriceCalculatorForB2BInsideEU:
    @pytest.fixture
    def order_item_eu_b2b(self, order_item_factory, region_germany):
        return order_item_factory(
            order__region=region_germany,
            order__vat_type=VatType.PL_WDT_0.legacy_type,
            is_jetty=True,
        )

    @pytest.fixture
    def cart_item_eu_b2b(self, cart_item_factory, region_germany):
        return cart_item_factory(
            cart__region=region_germany,
            cart__vat_type=VatType.PL_WDT_0.legacy_type,
            is_jetty=True,
        )

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2b', OrderItemPriceCalculator],
            ['cart_item_eu_b2b', CartItemPriceCalculator],
        ],
    )
    def test_calculate_region_price(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        """Multiply shelf price by region and currency rate and subtract vat rate."""

        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.region_price == Decimal('234.51')
        assert calculated_item.region_price_net == Decimal('234.51')
        assert calculated_item.region_vat_amount == Decimal('0')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2b', OrderItemPriceCalculator],
            ['cart_item_eu_b2b', CartItemPriceCalculator],
        ],
    )
    def test_calculate_income_price(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        """Divide region price by currency rate and round."""
        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.price == Decimal('156')
        assert calculated_item.price_net == Decimal('156')
        assert calculated_item.vat_amount == Decimal('0')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2b', OrderItemPriceCalculator],
            ['cart_item_eu_b2b', CartItemPriceCalculator],
        ],
    )
    def test_calculate_assembly_price(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)
        mocker.patch('gallery.models.Jetty.get_assembly_price', return_value=42)
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        # assembly price - vat
        assert calculated_item.assembly_price == Decimal('60')
        # (assembly price - vat) * region rate * currency rate and round to integer
        assert calculated_item.region_assembly_price == Decimal('89')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_eu_b2b', OrderItemPriceCalculator],
            ['cart_item_eu_b2b', CartItemPriceCalculator],
        ],
    )
    def test_wrong_vat_number(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        """B2B with wrong VAT number should be charged with PL_NORMAL VAT rate."""

        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        instance.parent.vat_type = VatType.PL_NORMAL.legacy_type

        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.23'))
        assert calculated_item.region_price == Decimal('333')
        assert calculated_item.region_price_net == Decimal('270.73')
        assert calculated_item.region_vat_amount == Decimal('62.27')


@pytest.mark.django_db
class TestItemPriceCalculatorForCustomerOutsideEU:
    """
    For non-EU countries we should not charge VAT.

    However, instead reducing gross price by VAT, we mark gross price as netto price
    so we are like Robin Hood because these countries are rich.
    """

    @pytest.fixture
    def order_item_non_eu(self, order_item_factory, region_united_kingdom):
        return order_item_factory(
            order__region=region_united_kingdom,
            order__vat_type=VatType.PL_EXPORT_0.legacy_type,
            is_jetty=True,
        )

    @pytest.fixture
    def cart_item_non_eu(self, cart_item_factory, region_united_kingdom):
        return cart_item_factory(
            cart__region=region_united_kingdom,
            cart__vat_type=VatType.PL_EXPORT_0.legacy_type,
            is_jetty=True,
        )

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_non_eu', OrderItemPriceCalculator],
            ['cart_item_non_eu', CartItemPriceCalculator],
        ],
    )
    def test_calculate_region_price(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        """Multiply shelf price by region and currency rate, DON'T subtract vat rate."""
        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'regions.mixins.RegionCalculationsObject.calculate_regionalized',
            # prices for UK are rounded, so we want to mock this instead shelf price
            return_value=222,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.region_price == Decimal('222')
        assert calculated_item.region_price_net == Decimal('222')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_non_eu', OrderItemPriceCalculator],
            ['cart_item_non_eu', CartItemPriceCalculator],
        ],
    )
    def test_calculate_income_price(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        """Divide region price by currency rate, DON'T subtract vat rate, round."""
        instance = request.getfixturevalue(fixture_name)
        mocker.patch(
            'regions.mixins.RegionCalculationsObject.calculate_regionalized',
            # prices for UK are rounded, so we want to mock this instead shelf price
            return_value=222,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.price == Decimal('148')
        assert calculated_item.price_net == Decimal('148')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_non_eu', OrderItemPriceCalculator],
            ['cart_item_non_eu', CartItemPriceCalculator],
        ],
    )
    def test_calculate_vat_amount(
        self,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.vat_amount == Decimal('0')
        assert calculated_item.region_vat_amount == Decimal('0')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_non_eu', OrderItemPriceCalculator],
            ['cart_item_non_eu', CartItemPriceCalculator],
        ],
    )
    def test_calculate_assembly_price(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)
        mocker.patch('gallery.models.Jetty.get_assembly_price', return_value=42)
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.assembly_price == Decimal('84')
        # assemby price * currency rate
        assert calculated_item.region_assembly_price == Decimal('126')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_non_eu', OrderItemPriceCalculator],
            ['cart_item_non_eu', CartItemPriceCalculator],
        ],
    )
    def test_calculate_assembly_price_for_switzerland_is_half_as_much(
        self,
        mocker,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)
        instance.region.name = 'switzerland'
        mocker.patch('gallery.models.Jetty.get_assembly_price', return_value=42)
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.assembly_price == Decimal('42')
        # assemby price * currency rate
        assert calculated_item.region_assembly_price == Decimal('63')


@pytest.mark.django_db
class TestItemPriceCalculator:
    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item', OrderItemPriceCalculator],
            ['cart_item', CartItemPriceCalculator],
        ],
    )
    def test_calculate_price_with_zero_vat(
        self,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(
            vat_rate=Decimal('0'),
            with_save=True,
            for_datetime=None,
        )

        assert calculated_item.vat_amount == Decimal('0')
        assert calculated_item.region_vat_amount == Decimal('0')
        assert calculated_item.price_net == int(calculated_item.price_net)
        assert calculated_item.price == calculated_item.price_net
        assert calculated_item.region_price == calculated_item.region_price_net

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item', OrderItemPriceCalculator],
            ['cart_item', CartItemPriceCalculator],
        ],
    )
    def test_calculate_price_without_saving(
        self,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)
        pricing_dict = instance.common_fields
        calculator = price_calculator(instance)
        calculated_instance = calculator.calculate(
            vat_rate=Decimal('0.23'),
            with_save=False,
            for_datetime=None,
        )

        assert calculated_instance.common_fields != pricing_dict
        calculated_instance.refresh_from_db()
        assert calculated_instance.common_fields == pricing_dict

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_factory', OrderItemPriceCalculator],
            ['cart_factory', CartItemPriceCalculator],
        ],
    )
    def test_region_and_income_price_aka_total_price(
        self,
        mocker,
        region,
        currency_rate_factory,
        region_rate_factory,
        fixture_name,
        price_calculator,
        request,
    ):
        """
        A `price` of the order_item shall be equal `region_price` divided by regional
        and currency rates.
        """
        currency_rate_factory(currency=region.currency, rate=1.5)
        region_rate_factory(region=region, rate=2)

        shelf_price = 100
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=shelf_price,
        )
        region_price = (
            shelf_price * region.currency.current_rate.rate * region.current_rate.rate
        )
        parent_instance = request.getfixturevalue(fixture_name)(region=region)
        instance = parent_instance.items.last()
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate()

        assert calculated_item.region_price == region_price
        assert calculated_item.price == region_price / region.currency.current_rate.rate

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_item_factory', OrderItemPriceCalculator],
            ['cart_item_factory', CartItemPriceCalculator],
        ],
    )
    def test_calculate_price_with_free_assembly_service_remains_the_same(
        self,
        fixture_name,
        price_calculator,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)(
            free_assembly_service=True,
            is_watty=True,
        )

        assembly_price = instance.assembly_price

        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))
        assert calculated_item.assembly_price == assembly_price

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_factory', OrderItemPriceCalculator],
            ['cart_factory', CartItemPriceCalculator],
        ],
    )
    def test_calculate_price_with_other_region(
        self,
        fixture_name,
        price_calculator,
        mocker,
        region_factory,
        currency_rate_factory,
        region_rate_factory,
        request,
    ):
        region = region_factory(other=True, currency__rates=[])
        currency_rate_factory(currency=region.currency, rate=1.5)
        region_rate_factory(region=region, rate=2)

        parent_instance = request.getfixturevalue(fixture_name)(region=region)
        instance = parent_instance.items.last()
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=111,
        )
        calculator = price_calculator(instance)
        calculated_item = calculator.calculate(vat_rate=Decimal('0.42'))

        assert calculated_item.price == Decimal('222')
        assert calculated_item.price_net == Decimal('156.34')


@pytest.fixture
def create_region(
    country_factory,
    region_factory,
    currency_factory,
    currency_rate_factory,
    region_rate_factory,
):
    def _create_region(
        name: str,
        euro_currency: bool,
        currency_rate: Decimal,
        region_rate: Decimal,
        vat: Decimal,
    ):
        currency = currency_factory(rates=[], euro=euro_currency)
        region = region_factory(name=name, currency=currency, is_eu=True)

        country_factory(name=name, region=region, vat=vat)
        currency_rate_factory(currency=region.currency, rate=currency_rate)
        region_rate_factory(region=region, rate=region_rate)

        return region

    return _create_region


@pytest.mark.django_db
class TestOrderPriceCalculator:
    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_price_calculator_b2c_eu_country(
        self,
        fixture_name,
        price_calculator,
        mocker,
        region_germany_neutral,
        country_factory,
        request,
    ):
        region = region_germany_neutral
        region.country = country_factory(name='germany', vat=Decimal('0.33'))

        mocker.patch(
            'gallery.models.Jetty.get_shelf_price_as_number',
            return_value=1500,
        )
        instance = request.getfixturevalue(fixture_name)(region=region)

        calculator = price_calculator(instance)
        calculator.calculate()
        item = instance.items.last()

        assert item.price == Decimal('1500.00')
        assert item.price_net == Decimal('1127.82')
        assert item.vat_amount == Decimal('372.18')
        assert item.region_price == Decimal('1500.00')
        assert item.region_price_net == Decimal('1127.82')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    def test_price_calculator_b2b_but_wrong_vat_number(
        self,
        fixture_name,
        price_calculator,
        mocker,
        country_factory,
        region_germany_neutral,
        request,
    ):
        instance = request.getfixturevalue(fixture_name)(region=region_germany_neutral)

        instance.region.country = country_factory(name='germany', vat=Decimal('0.33'))
        instance.vat = 'DE123456789'

        mocker.patch(
            'gallery.models.Jetty.get_shelf_price_as_number',
            return_value=500,
        )
        price_calculator(instance).calculate(check_vat=True)

        assert instance.total_price == Decimal('1000.00')  # two jetties in the order
        assert instance.total_price_net == Decimal('813.01')  # 1000 / 1.23
        assert instance.vat_amount == Decimal('186.99')

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    @pytest.mark.parametrize('region_name', ('switzerland', 'united_kingdom', 'norway'))
    def test_price_calculator_sets_gross_as_net_for_rich_countries(
        self,
        fixture_name,
        price_calculator,
        region_name,
        region_factory,
        country_factory,
        mocker,
        request,
    ):
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=123,
        )
        region = region_factory(name=region_name)
        country_factory(vat=Decimal('0.11'), region=region)
        instance = request.getfixturevalue(fixture_name)(
            region=region,
            vat_type=VAT_EXPORT,
        )
        calculator = price_calculator(instance)
        calculator.calculate()
        expected_price = Decimal('246.00')
        assert instance.total_price_net == expected_price
        assert instance.total_price == expected_price

    @pytest.mark.parametrize(
        ['fixture_name', 'price_calculator'],
        [
            ['order_factory', OrderPriceCalculator],
            ['cart_factory', CartPriceCalculator],
        ],
    )
    @patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=666)
    def test_order_price_calculator_sets_proper_vat_amounts(
        self,
        _,
        fixture_name,
        price_calculator,
        region,
        country_factory,
        request,
    ):
        country_factory(vat=Decimal('0.11'), region=region)
        instance = request.getfixturevalue(fixture_name)(
            region=region,
            vat_type=VAT_NORMAL,
        )

        calculator = price_calculator(instance)
        calculator.calculate()

        assert int(instance.region_vat_amount) == int(
            instance.region_total_price_net * Decimal('0.11')
        )
        assert int(instance.vat_amount) == int(
            instance.total_price_net * Decimal('0.11')
        )

        item = instance.items.first()
        assert int(item.region_price_net) == int(item.region_price / Decimal('1.11'))
        assert int(item.price_net) == int(item.price / Decimal('1.11'))


@pytest.mark.django_db
class TestHandlePromoForItems:
    @pytest.mark.parametrize(
        ('shelf_price', 'voucher_value', 'voucher_type', 'expected_promo_amount'),
        [
            (1475, 27, VoucherType.PERCENTAGE, Decimal('398.00')),
            (1475, 475, VoucherType.ABSOLUTE, Decimal('475.00')),
        ],
    )
    def test_promo_amount_single_item(
        self,
        mocker,
        rates_neutral_region,
        order_factory,
        order_item_factory,
        voucher_factory,
        shelf_price,
        voucher_value,
        voucher_type,
        expected_promo_amount,
    ):
        mocker.patch(
            'gallery.models.Jetty.get_shelf_price_as_number',
            return_value=shelf_price,
        )
        voucher = voucher_factory(value=voucher_value, kind_of=voucher_type)
        order = order_factory(items=[], region=rates_neutral_region)
        order_item = order_item_factory(order=order, is_jetty=True)

        order.used_promo = voucher
        order.promo_text = voucher.code

        opc = OrderPriceCalculator(order)
        opc.calculate()
        order_item.refresh_from_db()

        assert order.promo_amount == expected_promo_amount
        assert order_item.region_promo_value == expected_promo_amount

    def test_percentage_promo_amount_on_many_items(
        self,
        rates_neutral_region,
        order_factory,
        order_item_factory,
        voucher_factory,
    ):
        order = order_factory(items=[], region=rates_neutral_region)
        order.region_total_price = Decimal('2200.50')

        oi_1 = order_item_factory(
            order=order, is_jetty=True, price_net=Decimal('500.36') / Decimal('1.23')
        )
        oi_2 = order_item_factory(
            order=order, is_jetty=True, price_net=Decimal('700.12') / Decimal('1.23')
        )
        oi_3 = order_item_factory(
            order=order, is_jetty=True, price_net=Decimal('1000.02') / Decimal('1.23')
        )

        voucher = voucher_factory(value=27, kind_of=VoucherType.PERCENTAGE)
        order.used_promo = voucher
        order.promo_text = voucher.code
        # fake apply the voucher to the order
        order.region_promo_amount = Decimal('594.14')

        opc = OrderPriceCalculator(order)
        opc._handle_promo_for_items()

        oi_1.refresh_from_db()
        oi_2.refresh_from_db()
        oi_3.refresh_from_db()

        assert oi_1.region_promo_value == Decimal('135.11')
        assert oi_2.region_promo_value == Decimal('189.03')
        assert oi_3.region_promo_value == Decimal('270.00')

        assert (
            sum(order.items.values_list('region_promo_value', flat=True))
            == order.region_promo_amount
        )

    def test_absolute_promo_amount_on_many_items(
        self,
        rates_neutral_region,
        order_factory,
        order_item_factory,
        voucher_factory,
    ):
        order = order_factory(
            items=[], region=rates_neutral_region, region_total_price=Decimal('2200')
        )

        item_1 = order_item_factory(
            order=order, is_jetty=True, price_net=Decimal('500') / Decimal('1.23')
        )
        item_2 = order_item_factory(
            order=order, is_jetty=True, price_net=Decimal('700') / Decimal('1.23')
        )
        item_3 = order_item_factory(
            order=order, is_jetty=True, price_net=Decimal('1000') / Decimal('1.23')
        )

        voucher = voucher_factory(value=1000, kind_of=VoucherType.ABSOLUTE)
        order.used_promo = voucher
        order.promo_text = voucher.code
        # fake apply the voucher to the order
        order.region_promo_amount = Decimal('1000')
        order.region_total_price = Decimal('2200') - Decimal('1000')

        opc = OrderPriceCalculator(order)
        opc._handle_promo_for_items()

        item_1.refresh_from_db()
        item_2.refresh_from_db()
        item_3.refresh_from_db()

        # 227.27 but missing cent goes here
        assert item_1.region_promo_value == Decimal('227.26')
        assert item_2.region_promo_value == Decimal('318.19')
        assert item_3.region_promo_value == Decimal('454.55')

        assert (
            sum(order.items.values_list('region_promo_value', flat=True))
            == order.region_promo_amount
        )

    def test_skip_items_not_included_in_discount(
        self,
        rates_neutral_region,
        order_factory,
        order_item_factory,
        voucher_factory,
        item_discount_factory,
    ):
        order = order_factory(items=[], region=rates_neutral_region)
        order.region_total_price = Decimal('13000')

        oi_1 = order_item_factory(
            order=order, is_jetty=True, price_net=Decimal('5000') / Decimal('1.23')
        )
        oi_2 = order_item_factory(
            order=order, is_watty=True, price_net=Decimal('7000') / Decimal('1.23')
        )
        oi_3 = order_item_factory(
            order=order, is_jetty=True, price_net=Decimal('1000') / Decimal('1.23')
        )

        voucher = voucher_factory(value=10, kind_of=VoucherType.PERCENTAGE)
        voucher.discounts.add(
            item_discount_factory(value=50, shelf_type=ShelfType.TYPE03.value)
        )
        voucher.save()

        order.used_promo = voucher
        order.promo_text = voucher.code
        # fake apply the voucher to the order
        order.region_promo_amount = Decimal('4500')

        opc = OrderPriceCalculator(order)
        opc._handle_promo_for_items()

        oi_1.refresh_from_db()
        oi_2.refresh_from_db()
        oi_3.refresh_from_db()

        assert oi_1.region_promo_value == Decimal('500')
        assert oi_2.region_promo_value == Decimal('3500')
        assert oi_3.region_promo_value == Decimal('500')

        assert (
            sum(order.items.values_list('region_promo_value', flat=True))
            == order.region_promo_amount
        )

    def test_promotion_changed_during_order_lifespan(
        self,
        rates_neutral_region,
        order_factory,
        order_item_factory,
        voucher_factory,
    ):
        order = order_factory(
            status=OrderStatus.DRAFT,
            items=[],
            region=rates_neutral_region,
        )

        oi_1 = order_item_factory(
            order=order,
            is_jetty=True,
            region=order.region,
        )
        voucher = voucher_factory(value=10, kind_of=VoucherType.PERCENTAGE)
        order.used_promo = voucher
        order.promo_text = voucher.code

        with mock.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=5000,
        ):
            OrderPriceCalculator(order).calculate()
            oi_1.refresh_from_db()
            assert oi_1.region_promo_value == Decimal('500')
            assert order.region_total_price == Decimal('4500')
            assert order.region_promo_amount == Decimal('500')

            voucher.end_date = timezone.now() - timedelta(days=1)
            voucher.save()
            oi_2 = order_item_factory(
                order=order,
                is_jetty=True,
                region=order.region,
            )
            order.refresh_from_db()
            OrderPriceCalculator(order).calculate()
            oi_1.refresh_from_db()
            oi_2.refresh_from_db()
            assert oi_1.region_promo_value == Decimal('0')
            assert oi_2.region_promo_value == Decimal('0')
            assert order.region_total_price == Decimal('10000')
            assert order.region_promo_amount == Decimal('0')


class DeliveryPriceCalculationCases:
    @pytest.mark.parametrize(
        ('region_fixture', 'expected_delivery_price'),
        (
            ['region_pl', Decimal('39.00')],
            ['region_fr', Decimal('69.00')],
            ['region_uk', Decimal('99.00')],
            ['region_fi', Decimal('249.00')],
            ['region_no', Decimal('399.00')],
        ),
    )
    def case_one_module_sotty(
        self,
        region_fixture,
        expected_delivery_price,
        sotty_factory,
        request,
    ):
        region = request.getfixturevalue(region_fixture)
        sotty = sotty_factory(one_module=True)
        return sotty, region, expected_delivery_price

    @pytest.mark.parametrize(
        ('region_fixture', 'expected_delivery_price'),
        (
            ['region_pl', Decimal('69.00')],
            ['region_fr', Decimal('99.00')],
            ['region_uk', Decimal('169.00')],
            ['region_fi', Decimal('399.00')],
            ['region_no', Decimal('599.00')],
        ),
    )
    def case_two_modules_sotty(
        self,
        region_fixture,
        expected_delivery_price,
        sotty_factory,
        request,
    ):
        region = request.getfixturevalue(region_fixture)
        sotty = sotty_factory(two_modules=True)
        return sotty, region, expected_delivery_price

    @pytest.mark.parametrize(
        ('region_fixture', 'expected_delivery_price'),
        (
            ['region_pl', Decimal('99.00')],
            ['region_fr', Decimal('169.00')],
            ['region_uk', Decimal('199.00')],
            ['region_fi', Decimal('599.00')],
            ['region_no', Decimal('899.00')],
        ),
    )
    def case_three_modules_sotty(
        self,
        region_fixture,
        expected_delivery_price,
        sotty_factory,
        request,
    ):
        region = request.getfixturevalue(region_fixture)
        sotty = sotty_factory(three_modules=True)
        return sotty, region, expected_delivery_price

    def case_covers_only(
        self,
        region_de,
        sotty_factory,
    ):
        sotty = sotty_factory(covers_only=True)
        return sotty, region_de, Decimal('0.00')


class TestDeliveryPriceCalculation:
    @parametrize_with_cases(
        'sotty, region, expected_delivery_price', cases=DeliveryPriceCalculationCases
    )
    def test_delivery_price_calculation(
        self,
        sotty,
        region,
        expected_delivery_price,
        cart_factory,
    ):
        cart = cart_factory(items=[], region=region)
        CartService(cart).add_to_cart(sotty)
        order = CartService(cart).sync_with_order()

        cart_item = CartItem.objects.get(cart=cart)
        order_item = OrderItem.objects.get(order=order)

        rco = RegionCalculationsObject(region=region)
        regionalized_expected_delivery_price = rco.calculate_regionalized(
            expected_delivery_price
        )
        assert cart.get_delivery_price_in_euro() == expected_delivery_price
        assert cart.get_delivery_price() == regionalized_expected_delivery_price
        assert order.get_delivery_price_in_euro() == expected_delivery_price
        assert order.get_delivery_price() == regionalized_expected_delivery_price
        assert cart_item.delivery_price == expected_delivery_price
        assert cart_item.region_delivery_price == regionalized_expected_delivery_price
        assert order_item.delivery_price == expected_delivery_price
        assert order_item.region_delivery_price == regionalized_expected_delivery_price


@pytest.mark.django_db
def test_promo_value_vs_toggling_additional_service(
    cart_with_sofa_and_old_sofa_collection,
    delivery_voucher,
    api_client,
):
    """Real life scenario. Germany, sotty, promo for delivery and turned on old sofa collection.
    After turning off old sofa collection, promo value has wrong values, should stay the same."""
    cart = cart_with_sofa_and_old_sofa_collection
    voucher_service = VoucherService(instance=cart, code=delivery_voucher.code)
    voucher_service.process_voucher()
    cart.refresh_from_db()
    promo_amount_before = cart.promo_amount

    # url = f'/api/v2/cart/{cart.id}/change_old_sofa_collection/'
    # api_client.force_authenticate(user=cart.owner)
    # response = api_client.post(url, {'old_sofa_collection': False})
    # assert response.status_code == 200
    service = CartService(cart)
    service.change_old_sofa_collection(activate=False)


    delivery_voucher.calculate_price_with_discounts(cart, cart.material_items.all())

    cart.refresh_from_db()
    promo_amount_after = cart.promo_amount
    assert promo_amount_after == promo_amount_before

