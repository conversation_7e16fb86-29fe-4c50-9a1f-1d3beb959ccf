from abc import abstractmethod
from decimal import Decimal
from typing import (
    TYPE_CHECKING,
    Generic,
    Optional,
    Type,
    TypeVar,
    Union,
)

from django.db.models import QuerySet
from django.utils import timezone
from django.utils.translation import gettext as _

from custom.metrics import metrics_client
from orders.choices import VatType
from orders.enums import OrderStatus
from orders.services.vat_details import VatDetailsGetter
from pricing_v3.services.item_price_calculators import (
    CartItemPriceCalculator,
    ItemPriceCalculatorBase,
    OrderItemPriceCalculator,
)
from regions.mixins import RegionCalculationsObject
from vouchers.services.voucher_validator import VoucherService
from vouchers.utils import convert_absolute_voucher_to_percentage

if TYPE_CHECKING:
    from carts.models import (
        Cart,
        CartItem,
    )
    from orders.models import (
        Order,
        OrderItem,
    )
    from vouchers.models import Voucher


TInstance = TypeVar('TInstance', bound=Union['Cart', 'Order'])
TItem = TypeVar('TItem', bound=Union['CartItem', 'OrderItem'])


class PriceCalculatorBase(Generic[TInstance, TItem]):
    """Base service responsible for calculating all price related fields.

    Works with Order and Cart models.
    """

    WITH_PRICE_UPDATED_AT_CHANGE: bool = True

    def __init__(self, instance: TInstance) -> None:
        self.instance = instance
        self.total = Decimal(0)
        self.base_total = Decimal(0)
        self.message_status: Optional[str] = None
        self.message_text: Optional[str] = None

        self.recalculate_items: bool = True

        self.vat_details = self.get_vat_details()
        self.region = instance.get_region(dont_use_cache=True)
        self.rco = RegionCalculationsObject(region=self.region)

    @abstractmethod
    def create_item_calculator(
        self, instance: TItem, rco: 'RegionCalculationsObject'
    ) -> 'ItemPriceCalculatorBase':
        """Creates a price calculator for the given item."""

    @property
    @abstractmethod
    def item_class(self) -> Type[TItem]:
        """Returns the class of child item."""

    def get_vat_details(self):
        return VatDetailsGetter(self.instance)

    def calculate(
        self,
        change_promo_quantity: bool = False,
        check_vat: bool = False,
        ignore_vouchers_check: bool = False,
        recalculate_items: bool = True,
    ) -> None:
        self.recalculate_items = recalculate_items
        if check_vat:
            vat_type = self.vat_details.vat_type
            self.instance.vat_type = vat_type.legacy_type
            self.instance.region_vat = vat_type == VatType.LOCAL_VAT
            self.instance.vat_rate = self.vat_details.get_vat_rate_for_display()

        self._handle_items()
        self._set_primary_pricing_fields()
        self._handle_promo(
            ignore_vouchers_check=ignore_vouchers_check,
            change_promo_quantity=change_promo_quantity,
        )
        self._set_secondary_pricing_fields()
        self._handle_promo_for_items()
        self.instance.save()

    def _handle_promo(
        self,
        ignore_vouchers_check: bool,
        change_promo_quantity: bool,
    ) -> None:
        if ignore_vouchers_check:
            return

        if not self.instance.promo_text:
            return self._handle_empty_promo_text()

        voucher_candidate = self._get_voucher_candidate()
        if not voucher_candidate:
            return self._handle_missing_voucher_candidate()

        self._update_promo_amounts(voucher_candidate)
        self.instance.used_promo = voucher_candidate

        if self._should_update_voucher_usage(voucher_candidate, change_promo_quantity):
            self._update_voucher_usage(voucher_candidate)

        self.message_status = 'ok'
        self.message_text = _('Promo accepted.')
        return

    def _get_voucher_candidate(self) -> Optional['Voucher']:
        voucher_service = VoucherService(self.instance, self.instance.promo_text)
        return voucher_service.voucher

    @staticmethod
    def _update_voucher_usage(voucher: 'Voucher') -> None:
        voucher.quantity_left -= 1
        voucher.save(update_fields=['quantity_left'])

        metrics_client().increment(
            'backend.voucher.use', 1, tags=[f'origin:{voucher.origin}']
        )

    def _handle_empty_promo_text(self) -> None:
        self.instance.clear_promo()
        self.message_status = 'ok'
        self.message_text = _('Promo removed')

    def _handle_missing_voucher_candidate(self) -> None:
        self._clear_promo()
        self.message_status = 'error'
        self.message_text = _('invalid_voucher_fallback')

    def _update_promo_amounts(self, voucher: 'Voucher') -> None:
        vat_rate = (
            Decimal('0') if self.instance.is_vat_exempt else self.vat_details.vat_rate
        )

        amount_after_voucher = voucher.calculate_amount_after_voucher(
            self.instance,
            region=self.region,
        )
        self.instance.region_promo_amount = self._crop_decimal(
            self.instance.region_total_price - amount_after_voucher
        )
        self.instance.region_promo_amount_net = self._crop_decimal(
            self.instance.region_promo_amount / (1 + vat_rate)
        )
        self.instance.promo_amount = self._get_income_promo_amount(
            self.instance.region_promo_amount
        )
        self.instance.promo_amount_net = self._crop_decimal(
            self.instance.promo_amount / (1 + vat_rate)
        )
        self.instance.region_total_price -= self.instance.region_promo_amount
        self.instance.total_price -= self.instance.promo_amount

    def _handle_items(self) -> None:
        for item in self._prefetch_furniture(self.instance.items.all()):
            self._handle_item(item=item)

    def _handle_item(self, item: TItem) -> None:
        if self.recalculate_items:
            item = self._calculate_item(item)

        self.total += item.aggregate_region_price + self._crop_decimal(
            item.aggregate_region_delivery_price
        )
        self.base_total += item.aggregate_price + self._crop_decimal(
            item.aggregate_delivery_price
        )
        if self.instance.assembly:
            self.total += self._crop_decimal(item.aggregate_region_assembly_price)
            self.base_total += self._crop_decimal(item.aggregate_assembly_price)

    def _set_primary_pricing_fields(self) -> None:
        self.instance.region_total_price = self._crop_decimal(self.total)
        self.instance.total_price = self._crop_decimal(self.base_total)

    def _set_secondary_pricing_fields(self) -> None:
        vat_rate = 0 if self.instance.is_vat_exempt else self.vat_details.vat_rate

        self.instance.region_total_price_net = self._crop_decimal(
            self.instance.region_total_price / (1 + vat_rate)
        )
        self.instance.region_vat_amount = (
            self.instance.region_total_price - self.instance.region_total_price_net
        )
        self.instance.total_price_net = self._crop_decimal(
            self.instance.total_price / (1 + vat_rate)
        )
        self.instance.vat_amount = (
            self.instance.total_price - self.instance.total_price_net
        )
        if self.WITH_PRICE_UPDATED_AT_CHANGE:
            self.instance.price_updated_at = timezone.now()

    @staticmethod
    def _crop_decimal(value: Union[Decimal, float, str, int]) -> Decimal:
        return Decimal(value).quantize(Decimal('.01'))

    def _get_income_promo_amount(self, region_price: Decimal) -> Decimal:
        base_promo_amount = self.rco.calculate_base(region_price)
        return (base_promo_amount * self.rco.region_rate).quantize(Decimal('.01'))

    @abstractmethod
    def _calculate_item(self, item: TItem) -> TItem:
        calculator = self.create_item_calculator(instance=item, rco=self.rco)
        return calculator.calculate(vat_rate=self.vat_details.vat_rate)

    @abstractmethod
    def _should_update_voucher_usage(
        self,
        voucher: 'Voucher',
        change_promo_quantity: bool,
    ) -> bool:
        pass

    @abstractmethod
    def _clear_promo(self) -> None:
        pass

    @abstractmethod
    def _prefetch_furniture(
        self,
        queryset: QuerySet[TItem],
    ) -> QuerySet[TItem]:
        pass

    def _handle_promo_for_items(self):
        if not self.instance.used_promo:
            return self.instance.items.update(region_promo_value=0)
        elif self.instance.used_promo.is_absolute():
            voucher_affected_items = self._handle_absolute_promo_for_items()
        elif self.instance.used_promo.is_percentage():
            voucher_affected_items = self._handle_percentage_promo_for_items()
        else:
            return
        self._handle_missing_cents_in_promo(voucher_affected_items)
        vals = [item.region_promo_value for item in voucher_affected_items]
        print(vals, 'dupa dupa')
        any_gowno = any([v < 0 for v in vals])
        if any_gowno:
            print("MAM CIE KURWA")


        self.item_class.objects.bulk_update(
            voucher_affected_items,
            [
                'region_promo_value',
                'region_delivery_price',
                'delivery_price',
                'region_delivery_promo_value',
            ],
        )

    def _handle_absolute_promo_for_items(self) -> list[TItem]:
        promo_value_rate = convert_absolute_voucher_to_percentage(self.instance)
        # all items are voucher affected, distribute promo evenly
        voucher_affected_items = list(
            self.instance.items.exclude(content_type__model='additionalservice')
        )
        for item in voucher_affected_items:
            calculator = self.create_item_calculator(instance=item, rco=self.rco)
            calculator.calculate_absolute_promo_price(rate=promo_value_rate)

        return voucher_affected_items

    def _handle_percentage_promo_for_items(self) -> list[TItem]:
        voucher_affected_items = self.instance.get_items_for_voucher()
        for item in voucher_affected_items:
            calculator = self.create_item_calculator(instance=item, rco=self.rco)
            calculator.calculate_percentage_promo_price(
                voucher=self.instance.used_promo
            )

        return voucher_affected_items

    def _handle_missing_cents_in_promo(self, items: list[TItem]) -> None:
        sum_promo_on_items = sum(item.aggregate_region_promo_value for item in items)
        missing_cents = self.instance.region_promo_amount - sum_promo_on_items

        if missing_cents:
            cheapest_item = min(
                items, key=lambda item: (item.quantity, item.region_promo_value)
            )
            cheapest_item.region_promo_value += self._crop_decimal(
                missing_cents / cheapest_item.quantity
            )


class CartPriceCalculator(PriceCalculatorBase['Cart', 'CartItem']):
    def create_item_calculator(
        self, instance: 'CartItem', rco: 'RegionCalculationsObject'
    ) -> 'CartItemPriceCalculator':
        return CartItemPriceCalculator(instance=instance, rco=rco)

    @property
    def item_class(self) -> Type['CartItem']:
        from carts.models import CartItem

        return CartItem

    def _should_update_voucher_usage(
        self,
        voucher: 'Voucher',
        change_promo_quantity: bool,
    ) -> bool:
        return voucher.quantity != -1 and change_promo_quantity

    def _clear_promo(self) -> None:
        self.instance.clear_promo()

    def _prefetch_furniture(
        self,
        queryset: QuerySet['CartItem'],
    ) -> QuerySet['CartItem']:
        lookup = 'cart_item'
        if lookup in queryset._prefetch_related_lookups:
            return queryset

        return queryset.prefetch_related(lookup)


class OrderPriceCalculator(PriceCalculatorBase['Order', 'OrderItem']):
    def __init__(self, order: 'Order') -> None:
        super().__init__(instance=order)

        # used when switching statuses, as defined in OrderSwitchStatusTransitionsMixin
        self.completed_target_items = order.completed_target_order_items.all()

    def create_item_calculator(
        self, instance: 'OrderItem', rco: 'RegionCalculationsObject'
    ) -> 'OrderItemPriceCalculator':
        return OrderItemPriceCalculator(instance=instance, rco=rco)

    @property
    def item_class(self) -> Type['OrderItem']:
        from orders.models import OrderItem

        return OrderItem

    def _calculate_item(self, item: 'OrderItem') -> 'OrderItem':
        if item not in self.completed_target_items:
            return super()._calculate_item(item)
        return item

    def _should_update_voucher_usage(
        self,
        voucher: 'Voucher',
        change_promo_quantity: bool,
    ) -> bool:
        return (
            voucher.quantity != -1
            and change_promo_quantity
            and not self.instance.is_suborder_for_split()
        )

    def _clear_promo(self) -> None:
        editable_statuses = [
            OrderStatus.CANCELLED,
            OrderStatus.DRAFT,
            OrderStatus.PAYMENT_FAILED,
            OrderStatus.CART,
        ]
        if self.instance.status in editable_statuses:
            self.instance.clear_promo()

    def _prefetch_furniture(
        self,
        queryset: QuerySet['OrderItem'],
    ) -> QuerySet['OrderItem']:
        lookup = 'order_item'
        if lookup in queryset._prefetch_related_lookups:
            return queryset

        return queryset.prefetch_related(lookup)


class OrderPriceCalculatorForPriceUpdatedAtPricing(OrderPriceCalculator):
    """
    It overrides _handle_promo so it doesn't call _get_voucher_candidate which
    triggers calculate_total_price_for_items from _get_voucher, so recalculates
    items even if recalculates_items=False
    """

    WITH_PRICE_UPDATED_AT_CHANGE: bool = False

    def __init__(self, order: 'Order', get_vat_type_from_order=True) -> None:
        self.get_vat_type_from_order = get_vat_type_from_order
        super().__init__(order)

    def get_vat_details(self):
        return VatDetailsGetter(
            self.instance,
            get_vat_type_from_order=self.get_vat_type_from_order,
        )

    def _handle_promo(
        self,
        ignore_vouchers_check: bool,
        change_promo_quantity: bool,
    ) -> None:
        if not self.instance.promo_text:
            return

        voucher = self.instance.used_promo
        self._update_promo_amounts(voucher)

        if self._should_update_voucher_usage(voucher, change_promo_quantity):
            self._update_voucher_usage(voucher)

        self.message_status = 'ok'
        self.message_text = _('Promo Code accepted.')
        return None

    def _calculate_item(self, item: 'OrderItem') -> 'OrderItem':
        if item not in self.completed_target_items:
            calculator = self.create_item_calculator(instance=item, rco=self.rco)
            item = calculator.calculate(
                vat_rate=self.vat_details.vat_rate,
                for_datetime=self.instance.price_updated_at,
            )
        return item
