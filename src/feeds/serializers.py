import base64
import urllib

from decimal import (
    ROUND_HALF_UP,
    Decimal,
)

from django.conf import settings
from django.core.files.base import ContentFile
from django.utils import translation

from rest_framework import serializers

from custom.enums import (
    Furniture,
    ShelfType,
)
from custom.utils.parameter_mappers import map_keys_in_dict
from feeds.constants import PRICE_RANGES
from feeds.enums import FeedItemGeneratedMethod
from feeds.image_configs import ImageConfigOption
from feeds.models import (
    FeedImage,
    FeedItem,
)
from feeds.utils import (
    GOOGLE_CATEGORY_MAPPING,
    truncate_sentence,
)
from gallery.models import (
    So<PERSON>,
    <PERSON><PERSON>,
)
from gallery.slugs import (
    get_title_for_furniture,
    get_title_for_grid,
)


class BaseFeedItemSerializer(serializers.Serializer):
    item_id = serializers.SerializerMethodField()
    title = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()
    shipping_price = serializers.SerializerMethodField()
    availability = serializers.ReadOnlyField(default='in stock')
    brand = serializers.ReadOnlyField(default='Tylko')
    image_link = serializers.SerializerMethodField()
    condition = serializers.ReadOnlyField(default='new')
    link = serializers.SerializerMethodField()
    google_product_category = serializers.ReadOnlyField(default='464')
    size = serializers.CharField(source='furniture.get_size')
    product_line = serializers.CharField(source='furniture.product_line')
    shelf_category = serializers.CharField(source='furniture.shelf_category')
    image_type = serializers.SerializerMethodField()
    price = serializers.SerializerMethodField()
    sale_price = serializers.SerializerMethodField()
    price_group = serializers.SerializerMethodField()
    style = serializers.SerializerMethodField()
    furniture_generated_method = serializers.SerializerMethodField()
    product_margin = serializers.DecimalField(
        max_digits=11,
        decimal_places=4,
    )

    @property
    def language(self):
        return self.context.get('feed').language.lower()

    @property
    def region(self):
        return self.context.get('region')

    def get_image(self, obj):
        return obj.prefetch_images[0] if obj.prefetch_images else None

    @staticmethod
    def get_furniture_generated_method(obj) -> str:
        return (
            FeedItemGeneratedMethod.AUTO
            if obj.category.auto_furniture_sync
            else FeedItemGeneratedMethod.MANUAL
        )

    def get_image_type(self, obj) -> str:
        try:
            img = self.get_image(obj)
            val = ImageConfigOption(img.config).extended_image_type
        except (ValueError, AttributeError):
            return ''
        if val == 'unreal_scene':
            return f'{val} {img.additional_data.get("scene", "NO DATA")}'
        return val

    def get_item_id(self, obj):
        item_id = f'{obj.furniture.id}C{obj.category.id}'
        if isinstance(obj.furniture, Sotty):
            item_id = f's{item_id}'
        elif isinstance(obj.furniture, Watty):
            item_id = f'w{item_id}'
        return item_id

    def get_title(self, obj):
        with translation.override(self.language):
            title = get_title_for_grid(obj.furniture, self.language)
        return truncate_sentence(title, 100)

    def get_color(self, obj):
        with translation.override(self.language):
            return obj.furniture.color.translated_simple_color.title()

    def get_description(self, obj):
        with translation.override(self.language):
            description = get_title_for_furniture(obj.furniture, self.language)
        return truncate_sentence(description, 1000)

    def get_shipping_price(self, obj):
        delivery_price = Decimal(
            obj.furniture.get_delivery_price(region=self.region)
        ).quantize(Decimal('0.00'))
        return f'{delivery_price} {self.region.currency.code}'

    def get_link_params(self) -> dict:
        return {
            'feed_category': self.context.get('category').id,
        }

    def get_link(self, obj):
        params = self.get_link_params()
        with translation.override(self.language):
            if obj.furniture.furniture_type == Furniture.sotty.value:
                furniture_url = obj.furniture.get_item_url_with_region(self.region)
            else:
                furniture_url = obj.furniture.get_url_with_region(self.region)
            base_url = urllib.parse.urljoin(
                str(settings.SITE_URL),
                str(furniture_url),
            )
        return f'{base_url}?{urllib.parse.urlencode(params)}'

    def get_price(self, obj):
        price = self._get_price(obj)
        return f'{price:.2f} {self.region.currency.code}'

    def get_sale_price(self, obj):
        # According to best practices, the sale price should only be applied
        # when an active promotion with strikethrough prices is available.
        if not self.context.get('promotion'):
            return ''
        sale_price = self._get_sale_price(obj)
        return f'{sale_price:.2f} {self.region.currency.code}'

    def get_price_group(self, obj):
        sale_price = self._get_sale_price_in_euro(obj)

        for low, high, label in PRICE_RANGES:
            if low < sale_price <= high:
                return label

    def get_style(self, obj):
        if obj.furniture.product_type == Furniture.watty.value:
            return obj.furniture.get_style_name()

        return obj.furniture.get_pattern_name()

    def get_image_link(self, obj):
        try:
            return self.get_image(obj).image.url
        except (ValueError, AttributeError):
            return ''

    def to_representation(self, instance):
        data = super().to_representation(instance)
        return map_keys_in_dict(data, self.keys_to_representation)

    @property
    def keys_to_representation(self):
        return {
            'shipping(price)': 'shipping_price',
            'custom_label_0': 'product_line',
            'custom_label_1': 'furniture_generated_method',
            'custom_label_2': 'style',
            'custom_label_3': 'price_group',
            'custom_label_4': 'image_type',
            'custom_label_5': 'product_margin',
        }

    def _get_price(self, obj) -> Decimal:
        return Decimal(
            obj.furniture.get_regionalized_price(
                region=self.region,
                currency=self.region.currency,
            )
        ).quantize(Decimal('.00'))

    def _get_price_in_euro(self, obj) -> Decimal:
        return Decimal(obj.furniture.get_shelf_price_as_number()).quantize(
            Decimal('.00')
        )

    def _get_sale_price(self, obj) -> Decimal:
        return (
            Decimal(
                obj.furniture.get_sale_price(
                    self._get_price(obj),
                    self.context.get('promotion'),
                )
            )
            .quantize(Decimal('1'), rounding=ROUND_HALF_UP)
            .quantize(Decimal('.00'))
        )

    def _get_sale_price_in_euro(self, obj) -> Decimal:
        return (
            Decimal(
                obj.furniture.get_sale_price(
                    self._get_price_in_euro(obj),
                    self.context.get('promotion'),
                )
            )
            .quantize(Decimal('1'), rounding=ROUND_HALF_UP)
            .quantize(Decimal('.00'))
        )


class GoogleSerializer(BaseFeedItemSerializer):
    shipping_weight = serializers.DecimalField(
        source='furniture.get_estimated_weight_gross',
        max_digits=10,
        decimal_places=2,
    )
    product_type = serializers.SerializerMethodField()
    identifier_exists = serializers.ReadOnlyField(default='FALSE')
    length = serializers.SerializerMethodField()
    height = serializers.SerializerMethodField()
    depth = serializers.SerializerMethodField()
    furniture_material = serializers.SerializerMethodField()
    configurator = serializers.CharField(
        source='furniture.get_configurator_type_display'
    )
    google_product_category = serializers.SerializerMethodField()

    # energy efficiency class
    energy_efficiency_class = serializers.SerializerMethodField()
    min_energy_efficiency_class = serializers.SerializerMethodField()
    max_energy_efficiency_class = serializers.SerializerMethodField()

    @staticmethod
    def get_length(obj: FeedItem):
        return f'{obj.furniture.get_width()} cm'

    @staticmethod
    def get_height(obj: FeedItem):
        return f'{obj.furniture.get_height()} cm'

    @staticmethod
    def get_depth(obj: FeedItem):
        return f'{obj.furniture.get_depth()} cm'

    def get_product_type(self, obj: FeedItem) -> str:
        formatted_name = obj.furniture.color.name.replace('_', ' ').title()
        return f'{obj.furniture.furniture_category} > {formatted_name}'

    def get_google_product_category(self, obj: FeedItem) -> str:
        return GOOGLE_CATEGORY_MAPPING.get(obj.furniture.furniture_category, '464')

    def get_furniture_material(self, obj: FeedItem) -> str:
        return {
            ShelfType.TYPE01: 'plywood',
            ShelfType.VENEER_TYPE01: 'veneer',
            ShelfType.TYPE02: 'particle board',
            ShelfType.TYPE03: 'particle board + MDF',
            ShelfType.TYPE13: 'chipboard + plywood',
            ShelfType.VENEER_TYPE13: 'chipboard + wood effect laminate',
        }.get(obj.furniture.shelf_type)

    def get_energy_efficiency_class(self, obj: FeedItem) -> str:
        return 'F' if obj.has_lightning else ''

    def get_min_energy_efficiency_class(self, obj: FeedItem) -> str:
        return 'A' if obj.has_lightning else ''

    def get_max_energy_efficiency_class(self, obj: FeedItem) -> str:
        return 'G' if obj.has_lightning else ''

    @property
    def keys_to_representation(self):
        result = super().keys_to_representation
        result.update(
            {
                'id': 'item_id',
                'link': 'link',
                'mobile_link': 'link',
            }
        )

        return result


class FacebookSerializer(BaseFeedItemSerializer):
    id = serializers.SerializerMethodField()
    ios_url = serializers.CharField(source='furniture.get_app_deeplink')
    ios_app_store_id = serializers.ReadOnlyField(default=991055398)
    ios_app_name = serializers.ReadOnlyField(default='Tylko')

    def get_id(self, obj: FeedItem) -> str:
        item_id = f'{obj.object_id}'
        if isinstance(obj.furniture, Sotty):
            item_id += 's'
        elif isinstance(obj.furniture, Watty):
            item_id += 'w'
        return item_id

    @property
    def keys_to_representation(self):
        result = super().keys_to_representation
        result.update(
            {
                'content_id': 'item_id',
            }
        )

        return result


class CriteoSerializer(BaseFeedItemSerializer):
    shipping_weight = serializers.DecimalField(
        source='furniture.get_estimated_weight_gross',
        max_digits=10,
        decimal_places=2,
    )
    product_type = serializers.ReadOnlyField(default='465,500061,6372,447,4195,6358')
    ios_url = serializers.CharField(source='furniture.get_app_deeplink')
    ios_app_store_id = serializers.ReadOnlyField(default=991055398)
    ios_app_name = serializers.ReadOnlyField(default='Tylko')
    is_bundle = serializers.BooleanField(default=False)
    android_app_name = serializers.ReadOnlyField(default='Tylko')
    android_package = serializers.ReadOnlyField(default='com.tylko.furniture5')

    @property
    def keys_to_representation(self):
        result = super().keys_to_representation
        result.update(
            {
                'link': 'link',
                'mobile_link': 'link',
                'id': 'item_id',
                'cs1': 'image_link',
                'android_package': 'ios_url',
                'ios_url': 'ios_url',
            }
        )

        return result


class FeedImageSerializer(serializers.Serializer):
    item_id = serializers.IntegerField()
    config_id = serializers.IntegerField()
    magic_preview = serializers.CharField()

    def save(self):
        config_id = self.validated_data['config_id']
        feed_item = FeedItem.objects.get(pk=self.validated_data['item_id'])
        feed_image = FeedImage.objects.filter(
            config=config_id,
            items__content_type=feed_item.content_type,
            items__object_id=feed_item.object_id,
        ).first()
        if not feed_image:
            feed_image = FeedImage.objects.create(config=config_id)
        feed_image.items.add(feed_item)

        file_name = f'{feed_image.file_prefix}.png'
        feed_image.image = ContentFile(
            base64.b64decode(self.validated_data['magic_preview']),
            file_name,
        )
        feed_image.save()
