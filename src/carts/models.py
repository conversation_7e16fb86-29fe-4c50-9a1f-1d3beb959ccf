import logging

from decimal import Decimal
from typing import (
    Final,
    Iterable,
)

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.validators import (
    MaxValueValidator,
    MinValueValidator,
)
from django.db import models
from django.db.models import QuerySet

from safedelete.managers import SafeDeleteManager
from safedelete.models import SafeDeleteModel

from carts.choices import CartStatusChoices
from custom.enums import Furniture
from custom.models import Timestampable
from gallery.mixins import (
    CartOrderMixin,
    SellableItemMixin,
)
from gallery.models import Watty
from gallery.types import ProductType
from orders.choices import OrderSource
from orders.services.vat_details import VatDetailsGetter
from pricing_v3.models import (
    ItemPriceAbstractModel,
    PriceAbstractModel,
)
from pricing_v3.models.mixins import AggregateItemPriceMixin
from pricing_v3.services.item_price_calculators import CartItemPriceCalculator
from pricing_v3.services.price_calculators import CartPriceCalculator
from regions.mixins import RegionalizedMixin
from services.enums import AdditionalServiceKind
from services.models import AdditionalService

logger = logging.getLogger('cstm')
User = get_user_model()


class Cart(
    RegionalizedMixin,
    CartOrderMixin,
    SafeDeleteModel,
    PriceAbstractModel,
):
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='carts',
    )
    order = models.OneToOneField(
        'orders.Order',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    status = models.IntegerField(
        choices=CartStatusChoices.choices,
        default=CartStatusChoices.ACTIVE,
        db_index=True,
    )
    cart_source = models.IntegerField(
        choices=OrderSource.choices, default=OrderSource.UNKNOWN_SOURCE
    )

    vat = models.CharField('Vat number', max_length=256, blank=True, default='')
    invoice_country = models.CharField(
        'invoice country',
        max_length=50,
        null=True,
        blank=True,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    first_item_added_at = models.DateTimeField(null=True, blank=True)
    items_changed_at = models.DateTimeField(null=True, blank=True)
    price_updated_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return (
            f'Cart ({self.id}) - status: {self.get_status_display()} '
            f'- for user: {self.owner_id}'
        )

    @property
    def is_order(self) -> bool:
        return False

    @property
    def is_empty(self) -> bool:
        return not self.items.exists()

    def is_full_barter_deal(self) -> bool:
        if self.used_promo.is_legit_barter_deal(self.region):
            return self.region_total_price - self.used_promo.barter_deal.value <= 0

        return False

    def get_total_price_number(self) -> Decimal:
        if self.total_price is None:
            CartPriceCalculator(self).calculate(check_vat=True)
        return self.get_total_value()

    def calculate_total_price_for_items(self, items: list['CartItem']) -> float:
        """Additional method to calculate order items price for vouchers."""
        total = 0
        vat_details = VatDetailsGetter(self)
        for item in items:
            CartItemPriceCalculator(item).calculate(
                vat_rate=vat_details.vat_rate,
                # if order is B2B, we don't want to save recalculations for item
                with_save=not vat_details.vat_type.is_vat_exempt,
            )
            total += item.aggregate_region_price + item.aggregate_region_delivery_price
        return total

    def get_region_aggregated_furniture_price(self):
        # no assembly, no delivery, no extra services, just items in regional currency
        return sum(
            item.aggregate_region_price
            for item in self.items.exclude(content_type__model='additionalservice')
        )

    def clear_promo(self, commit: bool = True) -> None:
        self.promo_text = ''
        self.used_promo = None
        self.clear_promo_amounts()
        if commit:
            self.save(
                update_fields=[
                    'promo_text',
                    'used_promo',
                    'promo_amount',
                    'promo_amount_net',
                    'region_promo_amount',
                    'region_promo_amount_net',
                ]
            )

    def get_old_sofa_collection_service(self) -> AdditionalService | None:
        # iter through items.all() to keep prefetched data and limit DB queries
        for item in self.items.all():
            if (
                item.content_type.model == 'additionalservice'
                and item.content_type.app_label == 'services'
                and hasattr(item.cart_item, 'kind')
                and item.cart_item.kind == AdditionalServiceKind.OLD_SOFA_COLLECTION
            ):
                return item.cart_item
        return None

    def get_old_sofa_collection_price(self) -> Decimal:
        from services.services.old_sofa_collection import OldSofaCollectionService

        if not self.has_s01:
            return Decimal('0')
        return OldSofaCollectionService(self)._calculate_price()


class CartItemManager(SafeDeleteManager):
    def get_active_for_user(self, user: User) -> QuerySet['CartItem']:
        return (
            self.select_related('cart')
            .exclude(cart__status=CartStatusChoices.ORDERED)
            .filter(cart__owner=user)
        )

    def get_without_assembly_service_required(self) -> QuerySet['CartItem']:
        watty_ids = self.filter(content_type__model=Furniture.watty.value).values_list(
            'object_id', flat=True
        )
        watty_with_as_required_ids = Watty.objects.get_with_assembly_service_required(
            ids=watty_ids
        ).values_list('id', flat=True)
        return self.exclude(
            content_type__model=Furniture.watty.value,
            object_id__in=watty_with_as_required_ids,
        )


class CartItem(
    Timestampable,
    RegionalizedMixin,
    SellableItemMixin,
    AggregateItemPriceMixin,
    SafeDeleteModel,
    ItemPriceAbstractModel,
):
    cart = models.ForeignKey(
        Cart,
        related_name='items',
        on_delete=models.CASCADE,
    )
    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='samplebox')
            | models.Q(app_label='gallery', model='watty')
            | models.Q(app_label='gallery', model='sotty')
            | models.Q(app_label='services', model='additionalservice')
        ),
        on_delete=models.CASCADE,
    )
    object_id = models.PositiveIntegerField()
    cart_item = GenericForeignKey('content_type', 'object_id')

    quantity = models.PositiveSmallIntegerField(
        default=1, validators=[MinValueValidator(1), MaxValueValidator(100)]
    )
    with_assembly = models.BooleanField(
        default=False,
        verbose_name='assembly_enabled',
        help_text='Did client choose the assembly service or is it required',
    )

    objects = CartItemManager()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return (
            f'CartItem ({self.id}) - {self.furniture_type} '
            f'({self.object_id}) for Cart: {self.cart_id}'
        )

    def save(self, *args, **kwargs):
        if self.region_promo_value < 0:
            print('NO KURWA!')
            # raise ValueError('Negative promo value')
        super().save(*args, **kwargs)

    @property
    def furniture_type(self) -> str:
        # TODO refactor this. furniture type should return furniture type, not service
        if not self.sellable_item:
            return '-'
        if self.is_service:
            return str(self.sellable_item)
        return self.sellable_item.get_furniture_type()

    @property
    def sellable_item(self) -> ProductType:
        return self.cart_item

    @property
    def parent(self) -> Cart:
        return self.cart

    @property
    def fields_dict(self) -> dict:
        return self.common_fields | {
            'quantity': self.quantity,
            'with_assembly': self.with_assembly,
        }


CART_ITEM_FIELDS: Final[Iterable[str]] = CartItem().fields_dict.keys()
