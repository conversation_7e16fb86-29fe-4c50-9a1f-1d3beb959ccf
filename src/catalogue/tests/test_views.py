from unittest.mock import patch
from urllib.parse import urlencode

from django.urls import reverse

import pytest

from rest_framework import status

from catalogue.constants.merchandising_config import FF_TESTING_NAME
from catalogue.enums import FurnitureAttributesEnum
from catalogue.models import (
    BoardManualOrder,
    CatalogueEntry,
)
from catalogue.views import CatalogueView
from custom.enums import (
    GridLabelType,
    ShelfType,
    Type01Color,
    Type02Color,
)
from promotions.choices import SaleTypeChoices
from regions.models import Region
from regions.utils import reverse_with_region


@pytest.fixture()
def entries(catalogue_entry_factory):
    entries = []
    widths = [100, 101, 100, 200, 99, 90]
    for width in widths:
        entries.append(catalogue_entry_factory(furniture__width=width))
    return entries


@pytest.mark.django_db
class TestCatalogueView:
    url = reverse('catalogue:catalogue-main')

    def test_returns_proper_min_and_max_width(self, entries, api_client):
        response = api_client.get(self.url)
        data = response.json()
        assert data['minWidth'] == 90
        assert data['maxWidth'] == 200

    def test_return_price_with_discount_without_promotion(
        self, entries, api_client, region_de
    ):
        query_params = {'regionName': 'germany'}
        response = api_client.get(f'{self.url}?{urlencode(query_params)}')
        data = response.json()
        for result in data['results']:
            assert result['regionPrice'] == result['regionPriceWithDiscount']

    def test_return_price_with_discount_with_promotion(
        self, entries, api_client, region_de, global_strikethrough_promo_30
    ):
        query_params = {'regionName': 'germany'}
        response = api_client.get(f'{self.url}?{urlencode(query_params)}')
        data = response.json()
        for result in data['results']:
            assert result['regionPrice'] > result['regionPriceWithDiscount']

    def test_returns_special_filters_data(
        self,
        promotion_config,
        catalogue_entry_factory,
        api_client,
    ):
        ce1 = catalogue_entry_factory()
        ce1.attributes.add('sale')
        ce2 = catalogue_entry_factory()
        ce2.attributes.add('newArrival')

        response = api_client.get(self.url)
        data = response.json()

        assert data['specialFilters']['isSaleFilterAvailable'] is True
        assert data['specialFilters']['isNewProductsFilterAvailable'] is True
        assert data['specialFilters']['isTopSellerFilterAvailable'] is False

    def test_sale_available_filter_is_off_if_region_not_in_promo(
        self,
        promotion_config,
        catalogue_entry_factory,
        api_client,
        region_uk,
        region_de,
    ):
        promotion_config.enabled_regions.set([region_uk])
        promotion_config.save()
        assert promotion_config.promotion.active is True

        catalogue_entry = catalogue_entry_factory()
        catalogue_entry.attributes.add('sale')

        url = f'{self.url}?regionName={region_de.name}'
        response = api_client.get(url)
        data = response.json()

        assert data['specialFilters']['isSaleFilterAvailable'] is False

    def test_returns_current_promotion_data(
        self,
        promotion_config,
        promotion_config_copy_factory,
        entries,
        api_client,
    ):
        header = 'test_header'
        promotion_config_copy_factory(
            grid_copy_slot_1_header_1=header,
            config=promotion_config,
        )
        response = api_client.get(self.url)
        promo_data = response.json()['promo']
        assert promo_data['gridHeader'] == header

    def test_returns_categories_in_promotion(
        self,
        mocker,
        entries,
        api_client,
    ):
        mocker.patch(
            'catalogue.views.get_categories_in_promo',
            return_value={'bookcase': 'over9000%'},
        )
        response = api_client.get(self.url)
        categories_in_promotion = response.json()['categoriesInPromotion']
        assert categories_in_promotion['bookcase'] == 'over9000%'

    @pytest.mark.parametrize('sale_type', SaleTypeChoices.values)
    @patch('vouchers.models.Voucher.get_discount_value_for_item', return_value=25)
    @patch('custom.utils.grid._is_valid_for_voucher', return_value=True)
    def test_adds_appropriate_sale_type_to_promo_label(
        self,
        _,
        __,
        sale_type,
        promotion_config_factory,
        entries,
        api_client,
    ):
        promo_config = promotion_config_factory(
            sale_type=sale_type,
            grid_show_promo_value=True,
        )

        response = api_client.get(self.url)
        assert response.status_code == status.HTTP_200_OK

        promo_label = {
            'value': '-25%',
            'type': GridLabelType.PROMOTION.value,
            'saleType': promo_config.sale_type,
            'translatedName': promo_config.get_sale_type_display(),
        }
        for entry in response.json()['results']:
            assert promo_label in entry['labels']

    @pytest.mark.parametrize(
        ['shelf_type', 'color', 'result'],
        [
            [ShelfType.TYPE01, Type01Color.WHITE, False],
            [ShelfType.TYPE02, Type02Color.FOREST_GREEN, False],
            [ShelfType.TYPE02, Type02Color.REISINGERS_PINK, True],
        ],
    )
    def test_is_special_edition_filter_available(
        self,
        shelf_type,
        color,
        result,
        api_client,
        catalogue_entry_factory,
        jetty_factory,
    ):
        assert color.is_special_edition_color is result

        jetty = jetty_factory(shelf_type=shelf_type, material=color)
        catalogue_entry_factory(furniture=jetty)

        response = api_client.get(self.url)

        assert (
            response.data['specialFilters']['isSpecialEditionFilterAvailable'] is result
        )

    @pytest.mark.parametrize(
        ('region_name', 'expected_ordering_field'),
        [
            ('poland', 'profit_netto_order'),
            ('germany', 'profit_netto_order_de'),
            ('france', 'profit_netto_order_fr'),
            ('netherlands', 'profit_netto_order_nl'),
            ('united_kingdom', 'profit_netto_order_uk'),
            ('switzerland', 'profit_netto_order_ch'),
        ],
    )
    @patch('catalogue.views.is_active_feature_flag', return_value=False)
    def test_ordering_field_for_all_shelves(
        self,
        _,
        region_name,
        expected_ordering_field,
        countries,
        ab_test_factory,
        api_client,
        assembly_region_keys,
    ):
        ff_name = FF_TESTING_NAME
        ab_test_factory(
            codename=ff_name,
            active=True,
            rate_split=1,
            feature_flag=True,
        )
        api_client.cookies[ff_name] = 'nok'

        with patch(
            'catalogue.enums.StrategyEnum.get_order_field',
            return_value=expected_ordering_field,
        ) as mock_method:
            response = api_client.get(f'{self.url}?regionName={region_name}')

            assert response.status_code == 200
            region = Region.objects.get(name=region_name)
            mock_method.assert_called_with(region_name, region.country.code)

    @pytest.mark.parametrize(
        ('region_name', 'expected_ordering_field'),
        [
            ('poland', 'profit_netto_category_order'),
            ('germany', 'profit_netto_category_order_de'),
            ('france', 'profit_netto_category_order_fr'),
            ('netherlands', 'profit_netto_category_order_nl'),
            ('united_kingdom', 'profit_netto_category_order_uk'),
            ('switzerland', 'profit_netto_category_order_ch'),
        ],
    )
    @patch('catalogue.views.is_active_feature_flag', return_value=False)
    def test_ordering_field_for_categories(
        self,
        _,
        region_name,
        expected_ordering_field,
        countries,
        ab_test_factory,
        api_client,
    ):
        ff_name = FF_TESTING_NAME
        ab_test_factory(
            codename=ff_name,
            active=True,
            rate_split=1,
            feature_flag=True,
        )
        api_client.cookies[ff_name] = 'nok'

        with patch(
            'catalogue.enums.StrategyEnum.get_category_order_field',
            return_value=expected_ordering_field,
        ) as mock_method:
            response = api_client.get(
                f'{self.url}?regionName={region_name}&category=sideboard'
            )

            assert response.status_code == 200
            region = Region.objects.get(name=region_name)
            mock_method.assert_called_with(region_name, region.country.code)

    @pytest.mark.parametrize(
        ('region_name', 'expected_ordering_field'),
        [
            ('poland', 'test_profit_netto_order'),
            ('germany', 'test_profit_netto_order_de'),
            ('france', 'test_profit_netto_order_fr'),
            ('netherlands', 'test_profit_netto_order_nl'),
            ('united_kingdom', 'test_profit_netto_order_uk'),
            ('switzerland', 'test_profit_netto_order_ch'),
        ],
    )
    @patch('catalogue.views.is_active_feature_flag', return_value=True)
    def test_ordering_field_for_testing_all_shelves(
        self,
        _,
        region_name,
        expected_ordering_field,
        countries,
        ab_test_factory,
        api_client,
        assembly_region_keys,
    ):
        ff_name = FF_TESTING_NAME
        ab_test_factory(
            codename=ff_name,
            active=True,
            rate_split=1,
            feature_flag=True,
        )
        api_client.cookies[ff_name] = 'nok'
        with patch(
            'catalogue.enums.StrategyEnum.get_test_order_field',
            return_value=expected_ordering_field,
        ) as mock_method:
            response = api_client.get(f'{self.url}?regionName={region_name}')

            assert response.status_code == 200
            region = Region.objects.get(name=region_name)
            mock_method.assert_called_with(region_name, region.country.code)

    @pytest.mark.parametrize(
        ('region_name', 'expected_ordering_field'),
        [
            ('poland', 'test_profit_netto_category_order'),
            ('germany', 'test_profit_netto_category_order_de'),
            ('france', 'test_profit_netto_category_order_fr'),
            ('netherlands', 'test_profit_netto_category_order_nl'),
            ('united_kingdom', 'test_profit_netto_category_order_uk'),
            ('switzerland', 'test_profit_netto_category_order_ch'),
        ],
    )
    @patch('catalogue.views.is_active_feature_flag', return_value=True)
    def test_ordering_field_for_testing_categories(
        self,
        _,
        region_name,
        expected_ordering_field,
        countries,
        ab_test_factory,
        api_client,
    ):
        ff_name = FF_TESTING_NAME
        ab_test_factory(
            codename=ff_name,
            active=True,
            rate_split=1,
            feature_flag=True,
        )
        api_client.cookies[ff_name] = 'nok'

        with patch(
            'catalogue.enums.StrategyEnum.get_test_category_order_field',
            return_value=expected_ordering_field,
        ) as mock_method:
            response = api_client.get(
                f'{self.url}?regionName={region_name}&category=sideboard'
            )

            assert response.status_code == 200
            region = Region.objects.get(name=region_name)
            mock_method.assert_called_with(region_name, region.country.code)

    @pytest.mark.parametrize(
        ('ff_value', 'result_count'),
        [('ok', 5), ('nok', 3)],
    )
    def test_showing_hidden_entries(
        self,
        ff_value,
        result_count,
        catalogue_entry_factory,
        ab_test_factory,
        api_rf,
        countries,
    ):
        catalogue_entry_factory.create_batch(2, enabled=False)
        catalogue_entry_factory.create_batch(3, enabled=True)

        ff_name = FF_TESTING_NAME
        ab_test_factory(
            codename=ff_name,
            active=True,
            rate_split=1,
            feature_flag=True,
        )

        request = api_rf.get(f'{self.url}?regionName=germany')
        request.COOKIES[ff_name] = ff_value
        request.feature_flag_rates = {ff_name: 1}
        request.query_params = {'regionName': 'germany'}
        response = CatalogueView.as_view()(request)
        assert len(response.data.get('results')) == result_count


@pytest.mark.django_db
class TestCatalogueDetailsView:
    base_url = reverse('catalogue:catalogue-details')

    def test_available_colors_serialization(
        self,
        catalogue_entry_factory,
        api_client,
    ):
        entries = catalogue_entry_factory.create_batch(2)
        available_colors = [
            {'id': entry.object_id, 'material': entry.material} for entry in entries
        ]
        CatalogueEntry.objects.filter(id__in={e.id for e in entries}).update(
            available_colors=available_colors
        )
        entry = entries[0]
        url = f'{self.base_url}?furniture={entry.object_id},{entry.content_type.model}'

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        response_json['availableColors'] = [
            {
                'id': entry.object_id,
                'material': entry.material,
                'thumbnail': f'https://testserver{entry.product_unreal_thumbnail_image_webp.url}',
            }
            for entry in entries
        ]


@pytest.mark.django_db
class TestEntryVariantView:
    base_url = reverse('catalogue:catalogue-variant')

    def test_entry_variant_keys_present(
        self,
        catalogue_entry_factory,
        api_client,
    ):
        entry = catalogue_entry_factory()
        url = f'{self.base_url}?furniture={entry.object_id},{entry.content_type.model}&regionName=germany'
        required_fields = {
            'id',
            'furnitureType',
            'title',
            'material',
            'url',
            'productImageUrl',
            'lifestyleImageUrl',
            'regionPrice',
            'regionPriceInEuro',
            'regionPriceWithDiscount',
            'regionPriceWithDiscountInEuro',
            'discountValue',
        }

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert required_fields == set(response_json.keys())

    def test_entry_variant_sotty_path(
        self,
        catalogue_entry_factory,
        api_client,
        country_factory,
    ):
        germany_country = country_factory(germany=True)

        entry = catalogue_entry_factory(is_sotty=True)
        url = f'{self.base_url}?furniture={entry.object_id},{entry.content_type.model}&regionName={germany_country.region.name}'

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['url'] == reverse_with_region(
            'front-product-sotty',
            region_code=germany_country.code.lower(),
            args=(entry.furniture.furniture_category, entry.furniture.id),
        )


@pytest.mark.django_db
class TestTopSellerView:
    url = reverse('catalogue:top-seller')

    def test_post_creates_proper_attribute_for_jetty(
        self,
        catalogue_entry_factory,
        api_client,
        admin_user,
        jetty,
    ):
        catalogue_entry = catalogue_entry_factory(furniture=jetty)
        api_client.force_authenticate(admin_user)
        api_client.post(
            self.url,
            data={
                'furniture_type': catalogue_entry.content_type.name,
                'furniture_id': catalogue_entry.object_id,
            },
        )
        assert FurnitureAttributesEnum.TOP_SELLER in catalogue_entry.attributes.names()

    def test_post_creates_proper_attribute_for_watty(
        self,
        catalogue_entry_factory,
        api_client,
        admin_user,
        watty,
    ):
        catalogue_entry = catalogue_entry_factory(furniture=watty)
        api_client.force_authenticate(admin_user)
        response = api_client.post(
            self.url,
            data={
                'furniture_type': catalogue_entry.content_type.name,
                'furniture_id': catalogue_entry.object_id,
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert FurnitureAttributesEnum.TOP_SELLER in catalogue_entry.attributes.names()

    def test_delete_removes_proper_attribute(
        self,
        catalogue_entry,
        api_client,
        admin_user,
    ):
        catalogue_entry.attributes.add('topSeller')
        api_client.force_authenticate(admin_user)
        api_client.delete(
            self.url,
            data={
                'furniture_type': catalogue_entry.content_type.name,
                'furniture_id': catalogue_entry.object_id,
            },
        )
        assert (
            FurnitureAttributesEnum.TOP_SELLER not in catalogue_entry.attributes.names()
        )

    def test_delete_does_nothing_when_attribute_not_present(
        self,
        catalogue_entry,
        api_client,
        admin_user,
    ):
        api_client.force_authenticate(admin_user)
        assert 'topSeller' not in catalogue_entry.attributes.names()
        api_client.delete(
            self.url,
            data={
                'furniture_type': catalogue_entry.content_type.name,
                'furniture_id': catalogue_entry.object_id,
            },
        )
        assert (
            FurnitureAttributesEnum.TOP_SELLER not in catalogue_entry.attributes.names()
        )


@pytest.mark.django_db
class TestMinigridView:
    @pytest.mark.parametrize(
        ('minigrid_name', 'size'),
        [
            ('kids_sideboard', 4),
            ('kids_wardrobe', 2),
        ],
    )
    def test_returns_only_requested_minigrid(
        self,
        board_manual_order_factory,
        api_client,
        minigrid_name,
        size,
    ):
        board_name = f'minigrid={minigrid_name}'
        board_manual_order_factory.create_batch(board_name=board_name, size=size)

        url = reverse('catalogue:minigrid', args=(minigrid_name,))

        response = api_client.get(url, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == size
        assert 'categories' in response.data.keys()

    def test_returns_minigrid_in_correct_order(
        self,
        board_manual_order_factory,
        api_client,
    ):
        expected_order = {}
        for i in range(1, 6):
            bmo = board_manual_order_factory(
                board_name='minigrid=kids_sideboard',
                order=i,
            )
            expected_order[i] = bmo.entry.furniture.pk

        url = reverse('catalogue:minigrid', args=('kids_sideboard',))

        response = api_client.get(url, format='json')
        result_order = {
            i: entry['id'] for i, entry in enumerate(response.data['results'], start=1)
        }

        assert result_order == expected_order

    @pytest.mark.parametrize(
        ('filter_name', 'filter_value', 'count'),
        [
            ('category', 'wardrobe', 2),
            ('category', 'sideboard', 4),
            ('shelfType', '4', 1),
            ('shelfType', '0', 4),
        ],
    )
    def test_minigrid_filtering(
        self,
        watty_factory,
        jetty_factory,
        catalogue_entry_factory,
        board_manual_order_factory,
        api_client,
        filter_name,
        filter_value,
        count,
    ):
        minigrid_name = 'kids'
        board_name = f'minigrid={minigrid_name}'
        jetties = jetty_factory.create_batch(
            shelf_type=ShelfType.TYPE01,
            shelf_category='sideboard',
            size=4,
        )
        watties = []
        watties.append(watty_factory.create(shelf_type=ShelfType.TYPE13))
        watties.append(watty_factory.create(shelf_type=ShelfType.TYPE03))

        for furniture in jetties + watties:
            entry = catalogue_entry_factory(furniture=furniture)
            board_manual_order_factory(
                board_name=board_name,
                entry=entry,
            )

        url = reverse('catalogue:minigrid', args=(minigrid_name,))
        query = f'?{filter_name}={filter_value}'

        response = api_client.get(url + query, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == count


@pytest.mark.django_db
class TestBoardManualOrderView:
    url = reverse('catalogue:update-manual-override')

    def test_creates_board_manual_order(
        self,
        api_client,
        catalogue_entry,
        user,
    ):
        board_name = 'category=desk'
        api_client.force_authenticate(user)

        assert not BoardManualOrder.objects.exists()

        response = api_client.post(
            self.url,
            data={
                'board_name': board_name,
                'order': 1,
                'furniture_id': catalogue_entry.object_id,
                'furniture_type': catalogue_entry.content_type.name,
            },
            format='json',
        )

        assert response.status_code == status.HTTP_201_CREATED

        bmo = BoardManualOrder.objects.get(order=1, board_name=board_name)
        assert bmo.entry == catalogue_entry

    def test_update_board_manual_orders_with_heigher_order_when_creating(
        self, api_client, catalogue_entry, user, board_manual_order_factory
    ):
        board_name = 'minigrid=tylkosa'
        old_board = board_manual_order_factory(board_name=board_name, order=2)
        api_client.force_authenticate(user)

        response = api_client.post(
            self.url,
            data={
                'board_name': board_name,
                'order': 2,
                'furniture_id': catalogue_entry.object_id,
                'furniture_type': catalogue_entry.content_type.name,
            },
            format='json',
        )

        assert response.status_code == status.HTTP_201_CREATED
        old_board.refresh_from_db()
        assert old_board.order == 3

    def test_deletes_board_manual_order(
        self,
        api_client,
        board_manual_order_factory,
        user,
    ):
        board_name = 'category=desk'
        board_manual_order_factory(board_name=board_name, order=1)

        api_client.force_authenticate(user)

        response = api_client.delete(
            self.url,
            data={
                'board_name': board_name,
                'order': 1,
            },
            format='json',
        )

        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert not BoardManualOrder.objects.exists()

    def test_updates_board_manual_order(
        self,
        api_client,
        catalogue_entry_factory,
        board_manual_order_factory,
        user,
    ):
        old_entry = catalogue_entry_factory()
        board_name = 'category=desk'
        board_manual_order_factory(board_name=board_name, order=1, entry=old_entry)
        new_entry = catalogue_entry_factory()
        api_client.force_authenticate(user)

        response = api_client.put(
            self.url,
            data={
                'board_name': board_name,
                'order': 1,
                'furniture_id': new_entry.object_id,
                'furniture_type': new_entry.content_type.name,
            },
            format='json',
        )

        assert response.status_code == status.HTTP_200_OK

        bmo = BoardManualOrder.objects.get(order=1, board_name=board_name)
        assert bmo.entry == new_entry


@pytest.mark.django_db
class TestBoardCopyView:
    url = reverse('catalogue:board-copy')

    def test_board_is_created_when_board_with_new_name_does_not_exist(
        self,
        board_manual_order_factory,
        api_client,
        user,
    ):
        board_name_to_be_copied = 'category=desk'
        board_manual_order_factory.create_batch(
            board_name=board_name_to_be_copied,
            size=3,
        )
        new_board_name = 'category=desk__region=france'
        api_client.force_authenticate(user)

        assert not BoardManualOrder.objects.filter(board_name=new_board_name).exists()

        response = api_client.post(
            self.url,
            data={
                'board_to_be_copied_name': board_name_to_be_copied,
                'regions': ['france'],
            },
            format='json',
        )

        assert response.status_code == status.HTTP_201_CREATED
        assert BoardManualOrder.objects.filter(board_name=new_board_name).count() == 3

    def test_regional_board_is_overwritten(
        self,
        board_manual_order_factory,
        api_client,
        user,
    ):
        board_name_to_be_copied = 'category=desk'
        board_manual_order_factory.create_batch(
            board_name=board_name_to_be_copied,
            size=6,
        )
        regional_name = 'category=desk__region=france'
        board_manual_order_factory.create_batch(
            board_name=regional_name,
            size=3,
        )
        api_client.force_authenticate(user)

        assert BoardManualOrder.objects.filter(board_name=regional_name).count() == 3

        response = api_client.post(
            self.url,
            data={
                'board_to_be_copied_name': board_name_to_be_copied,
                'regions': ['france'],
            },
            format='json',
        )

        assert response.status_code == status.HTTP_201_CREATED
        assert BoardManualOrder.objects.filter(board_name=regional_name).count() == 6
