<template>
  <div ref="checkoutWrapper">
    <Head>
      <Title>{{ $t('checkout.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        v-bind:content="$t('checkout.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        v-bind:content="$t('checkout.meta.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        v-bind:content="$t('checkout.meta.description')"
      />
    </Head>

    <template v-if="checkout2025">
      <CheckoutMobileSummaryBarNew v-if="!isDesktopViewport" />
    </template>
    <template v-else>
      <CheckoutMobileSummaryBar v-if="!isDesktopViewport" />
    </template>

    <ClientOnly>
      <Teleport to="#checkout-teleports">
        <div class="lg:hidden p-16 border-t border-neutral-400 rounded-t-12 bg-white">
          <div class="lg:hidden">
            <ScartSimpleOption
              v-if="totalPrice"
              class="mb-8 !py-0"
              v-bind="{
                title: $t('checkout.vat.included_headline'),
                value: totalPrice,
                leftStyles: 'block semibold-16 text-neutral-900',
                rightStyles: 'block semibold-16 text-neutral-900'
              }"
            />
            <ScartSimpleOption
              class="!py-0 mb-8"
              v-bind="{
                title: $t('checkout.delivery.costs_info'),
                value: format(0),
                leftStyles: 'block normal-12 text-neutral-900',
                rightStyles: 'block normal-12 text-neutral-900'
              }"
            />
          </div>
          <template v-if="displayPaymentButton">
            <CheckoutPaymentButton
              v-if="checkout2025 && !isDesktopViewport && paymentButtonLoader"
            />
            <div
              v-else-if="checkout2025 && !isDesktopViewport && !paymentButtonLoader"
              class="relative h-48"
            >
              <UiDotsLoader
                bounce-class="bg-orange !w-16 !h-16 !mr-8"
              />
            </div>
          </template>
        </div>
      </Teleport>
    </ClientOnly>

    <CheckoutFrame
      v-if="!checkout2025"
      heading-id="delivery"
      v-bind:heading-text="!isOtherRegion ? $t('checkout.summary.headline') : $t('checkout.other_region.headline')"
    >
      <template #breadcrumbs>
        <CheckoutBreadcrumbs
          class="!mb-32"
          v-bind="{
            eventLabel: 'address',
            activeIndex: 1
          }"
        />
      </template>
      <template #default>
        <ClientOnly>
          <CheckoutOtherRegion v-if="isOtherRegion" />
          <CheckoutForm v-else />
        </ClientOnly>
      </template>
      <template #summary>
        <ClientOnly>
          <CheckoutStickyScroll>
            <template v-if="isDesktopViewport">
              <CheckoutSummary
                v-bind="{
                  displayDeliveryNotice: hasDeliveryTimeGap
                }"
              />
            </template>
          </CheckoutStickyScroll>
        </ClientOnly>
      </template>
    </CheckoutFrame>

    <CheckoutFrameNew
      v-else
      heading-id="delivery"
      v-bind:heading-text="!isOtherRegion ? `${$t('scart.checkout')} <span class='normal-14 text-neutral-700' data-testid='checkout-items-count'>(${cartItemsCount} ${$t('common.items')})</span>` : $t('checkout.other_region.headline')"
    >
      <template #breadcrumbs>
        <CheckoutBreadcrumbsNew
          class="!mb-[29px]"
          v-bind="{
            eventLabel: 'address',
            activeIndex: 1
          }"
        />
      </template>
      <template #default>
        <ClientOnly>
          <CheckoutOtherRegion
            v-if="isOtherRegion"
            class="bg-white"
          />
          <CheckoutFormNew
            v-else
          />
        </ClientOnly>
      </template>
      <template #summary>
        <ClientOnly>
          <CheckoutStickyScroll>
            <template v-if="isDesktopViewport">
              <CheckoutSummaryNew
                class="bg-white p-32"
                v-bind="{
                  pending: !formIsMounted,
                  displayPaymentButton,
                  displayAssemblyPrice: true,
                  displayNewsletter: true,
                  displayAssembly: false,
                  displayDeliveryNotice: hasDeliveryTimeGap
                }"
              />
            </template>
          </CheckoutStickyScroll>
        </ClientOnly>
      </template>
    </CheckoutFrameNew>

    <ClientOnly>
      <ModalDeliveryNotice
        v-if="!visited"
        v-model="isModalOpen"
      />
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useGlobal } from '~/stores/global';
import { useScartStore } from '~/stores/scart';
import { checkoutAnalytics } from '~/composables/checkout/checkoutAnalytics';

import useMq from '~/composables/useMq';
import usePrice from '~/composables/usePrice';
import useCartStatus from '~/composables/useCartStatus';
import handleBFCache from '~/composables/checkout/handleBFCache';

definePageMeta({
  key: () => {
    const route = useRoute();
    return `checkout${route.params?.orderId}`;
  },
  layout: 'checkout',
  middleware: ['auth', 'checkout-status-form']
});

const gtm = useGtm();
const route = useRoute();
const global = useGlobal();
const cart = useScartStore();

const { format } = usePrice();
const { isDesktopViewport } = useMq('lg');
const { fetchUserStatus } = useCartStatus();
const { paymentButtonLoader, displayPaymentButton } = useAdyenDropin();
const { getEcommerceEvent, checkoutGa4Event, checkout2025Event } = checkoutAnalytics();

const checkout2025 = global.AB_TEST_VALUE('new_checkout2025')?.isActive || global.AB_TEST_VALUE('checkout2025_100')?.isActive;

const { formIsMounted } = storeToRefs(useCheckoutStore());
const { cartId: storeCartId, cartItemsCount } = storeToRefs(global);

const totalPrice = computed(() => format(cart.totalPrice));
const isOtherRegion = computed(() => global.regionName === '_other');

const cartId = route.params.cartId || storeCartId.value;

handleBFCache();

await fetchUserStatus(cartId as string | null);

const visited = ref(false);
const isModalOpen = ref(false);

const hasDeliveryTimeGap = computed(() => {
  const deliveryTimes = cart.cartItems
    .filter(item => item.content_type !== 'samplebox')
    .map(item => parseInt(item.delivery_time))
    .filter(time => !isNaN(time));

  return deliveryTimes.some((time, index) => deliveryTimes.slice(index + 1).some(laterTime => Math.abs(time - laterTime) > 2));
});

onMounted(() => {
  if (checkout2025) {
    gtm?.push({ ecommerce: null });
    gtm?.push(checkout2025Event('begin_checkout'));
  } else {
    gtm?.push(getEcommerceEvent('1', 'checkout'));
    gtm?.push({ ecommerce: null });
    gtm?.push(checkoutGa4Event('begin_checkout'));
  }

  const localStorageValue = window.localStorage.getItem('hasDeliveryTimeGap');

  if (localStorageValue === 'true') {
    visited.value = true;
  }

  isModalOpen.value = hasDeliveryTimeGap.value;
});

useHead({
  link: [
    {
      rel: 'canonical',
      href: route.path
    }
  ]
});
</script>
