<template>
  <main class="lg:relative lg:bg-grey-600 lg:pb-128 reset-modern-navigation-page-padding">
    <div class="h-[150px] md:h-[300px] lg:h-[600px] overflow-hidden">
      <BasePicture
        picture-classes="overflow-hidden md:h-full"
        img-classes="w-full h-full object-cover object-bottom"
        type="M T SD LD"
        v-bind="{
          path: 'library/jumbo/',
          alt: $t('library.title'),
          isRetinaUploaded: false
        }"
      />
    </div>
    <section
      class="container-cstm mt-[-12px] lg-max:relative rounded-t-12 pt-32 lg:w-10/12 lg:mt-[-300px] mx-auto lg-max:bg-white lg:py-0"
      v-bind:class="furniture.length ? 'pb-48' : 'pb-32'"
    >
      <h1
        class="normal-32 text-grey-900 lg:text-white lg:absolute lg:bold-54 xl:bold-72 top-128"
        data-testid="library-header"
        v-html="$t('library.title')"
      />
      <div
        class="bg-white lg:inline-block rounded-t-12 lg:rounded-12 w-full lg:px-[10%] lg:py-64"
        v-bind:class="{ 'lg:h-[55vh]': !furniture.length }"
      >
        <template v-if="furniture.length">
          <h2
            class="bold-16 lg:bold-24 py-32 lg:pb-64 lg:pt-0"
            data-testid="library-header-with-items-count"
            v-html="$t('library.wishlist_items', { items: furnitureCount })"
          />
          <WishlistItem
            v-for="(item, index) in furniture"
            v-bind:key="index"
            data-testid="wishlist-item"
            v-bind="{
              preview: item.preview,
              itemPriceWithoutDiscountRegionalizedNumber: +item.region_price,
              itemPriceRegionalizedNumber: +item.region_price_with_discount,
              promotion: item.promotion,
              title: item.title,
              url: item.url,
              id: item.id,
              configuratorType: item.configurator_type,
              width: +item.width,
              depth: +item.depth,
              height: +item.height,
              shelfType: item.shelf_type,
              category: item.category,
              position: item.position,
              timeToDelivery: item.time_to_delivery,
              omnibusPrice: item.omnibus_price,
              fabric: item.fabric,
            }"
            v-on:get-ecommerce-product-remove="getEcommerceProductRemove"
            v-on:get-ecommerce-product-add="getEcommerceProductAdd"
            v-on:update-furniture-list="updateFurnitureList"
          />
          <div
            v-if="next"
            class="flex flex-col items-center justify-center"
          >
            <Transition
              name="fade"
              mode="out-in"
            >
              <UiDotsLoader
                v-if="isFetching"
                class="h-40 !static !transform-none"
                bounce-class="bg-orange"
              />
              <template v-else>
                <BaseButton
                  variant="outlined"
                  class="whitespace-nowrap"
                  v-bind="{ trackData: {} }"
                  v-on="{
                    click: showMoreItems,
                  }"
                >
                  {{ $t('plp.board.button.more') }}
                </BaseButton>
              </template>
            </Transition>
          </div>
          <p class="mt-4 mb-32 normal-12 text-grey-900 text-center">
            {{ $t('plp.board.current_view_1') }} {{ furniture.length }} {{ $t('plp.board.current_view_2') }}
            {{ furnitureCount }}
          </p>
        </template>
        <template v-else>
          <h2
            class="bold-16 lg:bold-24 py-32 lg:pb-64 lg:pt-0"
            v-html="$t('library.empty_wishlist_heading')"
          />
          <BaseLink
            variant="outlined"
            data-testid="library-continue-shopping-button"
            v-bind="{
              href: $addLocaleToPath('grid'),
              trackData: { eventLabel: $t('library.continue_shopping_button'), eventPath: $addLocaleToPath('grid') }
            }"
          >
            {{ $t('library.continue_shopping_button') }}
          </BaseLink>
        </template>
      </div>
    </section>
  </main>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toastification';
import { useGlobal } from '~/stores/global';
import { GET_WISHLIST, GET_WISHLIST_NEXT } from '~/api/wishlist';
import { libraryAnalytics } from '~/composables/library/libraryAnalytics';

const { $addLocaleToPath, $logException } = useNuxtApp();
const { furniture, getEcommerceProductRemoveEvent, a2cGa4Event } = libraryAnalytics();

const gtm = useGtm();
const toast = useToast();
const global = useGlobal();

const { t } = useI18n();

const next = ref<string | null>(null);
const isFetching = ref(false);
const furnitureCount = ref(0);

const { data, error }: { data: any, error: any } = await GET_WISHLIST();

if (error.value) {
  toast.error(t('common.error.connection'));
  $logException(error.value);
} else {
  furniture.value = data.value.results;
  furnitureCount.value = data.value.count;
  next.value = data.value.next;
}

const showMoreItems = async () => {
  if (!isFetching.value) {
    isFetching.value = true;

    const nextWishlistItems: any = await GET_WISHLIST_NEXT(next.value as string);

    if (furniture.value) {
      furniture.value.push(...nextWishlistItems.data.value.results);
    }

    next.value = nextWishlistItems.data.value.next;
    isFetching.value = false;
  }
};

const updateFurnitureList = (data: any) => {
  furniture.value = data.results;
  furnitureCount.value = data.count;

  global.UPDATE_LIBRARY_ITEMS_COUNT(data.count);
};

const getEcommerceProductRemove = (id: number) => {
  gtm && gtm.push(getEcommerceProductRemoveEvent(id));
};

const getEcommerceProductAdd = async (id: number) => {
  gtm && gtm.push({ ecommerce: null });

  const eventData = await a2cGa4Event(id);

  if (eventData) {
    gtm && gtm.push({
      ...eventData
    });
  }
};

useSeoMeta({
  title: () => t('library.meta.title'),
  ogTitle: () => t('library.meta.title'),
  description: () => t('library.meta.description'),
  ogDescription: () => t('library.meta.description')
});
</script>
