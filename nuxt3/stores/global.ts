import { defineStore } from 'pinia';
import type { UseFetchOptions } from 'nuxt/app';
import type { RegionName } from '~/consts/regions';

export interface RegionApiData {
  regionCode: string,
  regionName: RegionName,
  currencyCode: string,
  countryLocale: string,
  t03Available: boolean,
  s01Available: boolean,
  corduroyAvailable: boolean,
  assemblyAvailable: boolean,
  featureFlags: Array<string>,
  newsletterVoucherValue: string,
  samplePrice: number,
  samplePromoPrice: number,
  availableLanguages: Array<Language>,
  storageSamplePrice: number,
  storageSamplePromoPrice: number,
  isStorageSamplePromoActive: boolean,
  sofaSamplePrice: number,
  sofaSamplePromoPrice: number,
  isSofaSamplePromoActive: boolean,
  oldSofaCollectionAvailable: boolean
}
export interface UserGlobal {
  globalSessionId: string,
  userId: string,
  userType: string,
  userHashId: string,
  userHashEmail: string,
  userHashEmailNoHmac: string,
  userLanguage: Language,
  isSignedIn: boolean,
  cartId: string,
  orderId: string,
  hasS01: boolean,
  hasCorduroy: boolean,
  hasT03: boolean,
  cartItemsCount: number,
  libraryItemsCount: number,
  abIds: any,
  abTestsList: [],
  featureFlagsIds: any,
  comesFromFacebook: boolean,
  hasOldSofaCollection: boolean
}
export interface CartRibbonData {
  enabled: boolean,
  headerMobile: string,
  header: string
}
export interface CountdownData {
  enabled: boolean,
  showOnPdp: boolean,
  endDate: string,
  textColor: string
}
export interface RibbonData {
  enabled: boolean,
  textColor: string,
  backgroundColor: string,
  header1: string,
  headerMobile1: string,
  header2?: string,
  headerMobile2?: string,
  link: string | null
}
export interface SamplesData {
  storageSamplePrice: number,
  storageSamplePromoPrice: number,
  isStorageSamplePromoActive: boolean,
  sofaSamplePrice: number,
  sofaSamplePromoPrice: number,
  isSofaSamplePromoActive: boolean,
}
export interface GlobalState {
  hasOldSofaCollection: boolean | null
  cartId: string | null;
  cookies: string;
  userLanguage: string | null;
  userId: string | null;
  userType: string | null;
  userHashId: string;
  userHashEmail: string;
  userHashEmailNoHmac: string;
  globalSessionId: string;
  isSignedIn: boolean;
  cartItemsCount: number | null;
  hasS01: boolean | null;
  hasCorduroy: boolean | null;
  hasT03: boolean | null;
  libraryItemsCount: number | null;
  brazeNotificationCount: number,
  t03Available: boolean;
  s01Available: boolean;
  corduroyAvailable: boolean;
  assemblyAvailable: boolean;
  regionCode: string;
  regionName: RegionName;
  currencyCode: string;
  countryLocale: string;
  promoIsActive: boolean;
  ribbon: RibbonData | null;
  countdown: CountdownData | null;
  cartRibbon: CartRibbonData | null;
  extraData: JSON | null;
  newsletterVoucherValue: any | null;
  abIds: any | null;
  abTestsList: any | null;
  featureFlagsIds: any | null;
  featureFlagsList: Array<string> | null;
  apiError: boolean;
  reviewsAverageScore: number;
  reviewsCount: number;
  isSalesEnabled: boolean | null;
  waitingListTokenActive: boolean | null;
  waitingListTokenExpired: boolean | null;
  samplePrice: number;
  samplePromoPrice: number;
  orderId: string | null;
  previousRoute: string | null;
  pdpProductCategory: string | null;
  oldSofaCollectionAvailable: boolean
  device: {
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    isWebView: boolean;
    iOSappVersion: string | null;
    isT13AvailableInCurrentIOSAppVersion: boolean;
  };
  reviews: {
    [key: string]: {
      rating: number;
      count: number;
    };
  };
  availableLanguages: Array<Language>;
  comesFromFacebook: boolean;
  isGlobalMounted: boolean;
  samplesData: SamplesData;
}

export const useGlobal = defineStore('global', {
  state: (): GlobalState => ({
    cartId: null,
    cookies: '',
    userLanguage: null,
    userId: null, // bez cache
    globalSessionId: '', // bez cache
    userType: null, // bez cache
    userHashId: '', // bez cache
    userHashEmail: '', // bez cache
    userHashEmailNoHmac: '',
    isSignedIn: false, // bez cache - do przerobnienia
    cartItemsCount: null, // bez cache
    hasS01: null, // cache - osobny endpoint
    hasCorduroy: null, // cache - osobny endpoint
    hasT03: null, // cache - osobny endpoint
    libraryItemsCount: null, // bez cache
    brazeNotificationCount: 0,
    t03Available: false, // cache - osobny endpoint
    corduroyAvailable: false, // cache - osobny endpoint
    s01Available: false, // cache - osobny endpoint
    assemblyAvailable: false, // cache - osobny endpoint
    regionCode: 'OT', // bez cache
    regionName: '_other', // bez cache
    currencyCode: 'EUR', // bez cache
    countryLocale: 'de_DE', // bez cache
    promoIsActive: false, // do innego endpointu
    ribbon: null, // do innego endpointu
    countdown: null,
    cartRibbon: null, // do innego endpointu / do wywalenia
    extraData: null,
    newsletterVoucherValue: null, // do innego endpointu
    abIds: null, // bez cache - do zastanowienia co z tym
    abTestsList: null, // bez cache - do zastanowienia co z tym
    featureFlagsIds: null, // bez cache - do zastanowienia co z tym
    featureFlagsList: null, // bez cache - do zastanowienia co z tym
    apiError: false, // co to jest
    reviewsAverageScore: 0, // do innego endpointu
    reviewsCount: 0, // do innego endpointu
    isSalesEnabled: null, // do wywalenia
    waitingListTokenActive: null, // do wywalenia
    waitingListTokenExpired: null, // do wywalenia
    samplePrice: 0, // do wywalenia
    samplePromoPrice: 0,
    orderId: null, // bez cache
    previousRoute: null,
    pdpProductCategory: null, // do wywalenia
    device: {
      isMobile: true,
      isTablet: false,
      isDesktop: false,
      isWebView: false,
      iOSappVersion: null,
      isT13AvailableInCurrentIOSAppVersion: false
    },
    reviews: { // do wywalenia
      jetty: {
        rating: 4.9,
        count: 7552
      },
      watty: {
        rating: 5,
        count: 20
      }
    },
    availableLanguages: [],
    comesFromFacebook: false,
    samplesData: {
      storageSamplePrice: 0,
      storageSamplePromoPrice: 0,
      isStorageSamplePromoActive: false,
      sofaSamplePrice: 0,
      sofaSamplePromoPrice: 0,
      isSofaSamplePromoActive: false
    },
    isGlobalMounted: false,
    oldSofaCollectionAvailable: false,
    hasOldSofaCollection: null
  }),
  getters: {
    IS_USER_AUTHENTICATED: state => !!state.globalSessionId,
    IS_GLOBAL_FETCHED: state => !!state.globalSessionId,
    USER_ID: state => state.userId,
    CART_ID: state => state.cartId,
    USER_TYPE: state => state.userType,
    USER_HASH_ID: state => state.userHashId || '',
    USER_HASH_EMAIL: state => state.userHashEmail || '',
    USER_HASH_EMAIL_NO_HMAC: state => state.userHashEmailNoHmac || '',
    IS_SIGNED_IN: state => state.isSignedIn,
    CART_ITEMS_COUNT: state => state.cartItemsCount,
    LIBRARY_ITEMS_COUNT: state => state.libraryItemsCount,
    T03_AVAILABLE: state => state.t03Available,
    CORDUROY_AVAILABLE: state => state.corduroyAvailable,
    S01_AVAILABLE: state => state.s01Available,
    HAS_T03: state => state.hasT03,
    HAS_S01: state => state.hasS01,
    HAS_CORDUROY: state => state.hasCorduroy,
    ASSEMBLY_AVAILABLE: state => state.assemblyAvailable,
    REGION_CODE: state => state.regionCode,
    REGION_NAME: state => state.regionName,
    CURRENCY_CODE: state => state.currencyCode,
    LOCALE: state => state.countryLocale,
    PROMO_IS_ACTIVE: state => state.promoIsActive,
    RIBBON: state => state.ribbon,
    COUNTDOWN: state => state.countdown,
    CART_RIBBON: state => state.cartRibbon,
    NEWSLETTER_VOUCHER_VALUE: state => state.newsletterVoucherValue,
    AB_IDS: state => state.abIds,
    AB_TESTS_LIST: state => state.abTestsList,

    AB_TEST_VALUE: state => (name: string) => {
      if (!state.abTestsList) {
        return false;
      } else {
        return state.abTestsList[name] ? state.abTestsList[name] : false;
      }
    },
    FEATURE_FLAGS_IDS: state => state.featureFlagsIds,
    FEATURE_FLAGS_LIST: state => state.featureFlagsList,
    FEATURE_FLAG_VALUE: state => (name: any) => {
      if (!state.featureFlagsList) {
        return false;
      }

      if (name === 't13') {
        const isApp = state.device.isWebView;
        const isT13AvailableInCurrentIOSAppVersion = state.device.isT13AvailableInCurrentIOSAppVersion;

        if (isApp && isT13AvailableInCurrentIOSAppVersion) {
          return state.featureFlagsList.includes(name);
        } else if (state.device.isWebView && !isT13AvailableInCurrentIOSAppVersion) {
          return false;
        }

        return state.featureFlagsList?.includes(name);
      }

      return state.featureFlagsList?.includes(name);
    },
    REVIEWS: state => (name: any) => state.reviews[name],
    IS_API_ERROR: state => state.apiError,
    REVIEWS_COUNT: state => state.reviewsCount,
    REVIEWS_AVERAGE_SCORE: state => state.reviewsAverageScore,
    IS_SALES_ENABLED: state => state.isSalesEnabled,
    WAITING_LIST_TOKEN_ACTIVE: state => state.waitingListTokenActive,
    WAITING_LIST_TOKEN_EXPIRED: state => state.waitingListTokenExpired,
    SAMPLE_PRICE: state => state.samplePrice,
    SAMPLE_PRICE_PROMO: state => state.samplePromoPrice,
    ORDER_ID: state => state.orderId,
    IS_WEBVIEW: state => state.device.isWebView,
    IS_DESKTOP: state => state.device.isDesktop,
    IS_MOBILE: state => state.device.isMobile,
    WHOLE_STORE: state => state,
    GET_PREVIOUS_ROUTE: state => state.previousRoute,
    COLORS: (state): any => state.colors,
    CATEGORIES: (state): any => state.categories,
    AB_TESTS_NAVIGATION_2025: (state): boolean => !!state?.abTestsList?.test_main_nav_2025?.isActive,
    IS_SMOOTH: (state): boolean => !!state?.featureFlagsList?.includes('ff_smooth_products') && !!state.s01Available,
    PDP_PRODUCT_CATEGORY: state => state.pdpProductCategory,
    IS_USER_COMES_FROM_FACEBOOK: state => !!(state.comesFromFacebook &&
      (state.featureFlagsList?.includes('meta_traffic_cta_order_reversed') ||
        state.abTestsList?.meta_traffic_cta_order_reversed?.isActive)),
    SAMPLES: state => state.samplesData
  },
  actions: {
    async FETCH_GLOBAL (options: UseFetchOptions<UserGlobal> = {}) {
      try {
        const {
          data,
          error
        } = await useApi<UserGlobal>('/api/v1/user-global/', options);

        if (error.value) {
          console.error(error.value);
        } else if (data.value) {
          this.SET_USER_GLOBAL(data.value);
        }
      } catch (e) {

      }
    },
    SET_COOKIES (data) {
      this.cookies = data;
    },
    SET_OLD_SOFA_COLLECTION (data: boolean) {
      this.hasOldSofaCollection = data;
    },
    SET_USER_GLOBAL (data: UserGlobal) {
      this.hasOldSofaCollection = data.hasOldSofaCollection;
      this.comesFromFacebook = data.comesFromFacebook;
      this.globalSessionId = data.globalSessionId;
      this.userId = data.userId;
      this.userType = data.userType;
      this.userHashId = data.userHashId;
      this.userHashEmail = data.userHashEmail;
      this.userHashEmailNoHmac = data.userHashEmailNoHmac;
      this.userLanguage = data.userLanguage;
      this.isSignedIn = data.isSignedIn;
      this.cartId = data.cartId;
      this.orderId = data.orderId;
      this.hasS01 = data.hasS01;
      this.hasCorduroy = data.hasCorduroy;
      this.hasT03 = data.hasT03;
      this.cartItemsCount = data.cartItemsCount;
      this.libraryItemsCount = data.libraryItemsCount;
      this.abIds = data.abIds;
      this.abTestsList = data.abTestsList
        .map((item: any) => ({
          name: item[0].substring(0, item[0].indexOf('|')),
          isActive: item[1],
          percentage: item[0].substring(item[0].indexOf('|') + 1)
        }))
        .reduce((obj: any, item: any) => {
          obj[item.name] = item;
          delete item.name;
          return obj;
        }, {});
      this.featureFlagsIds = data.featureFlagsIds;
    },
    UPDATE_ORDER_ID (orderId: string) {
      this.orderId = orderId;
    },
    UPDATE_CART_ID (cartId: string) {
      this.cartId = cartId;
    },
    SET_REVIEWS (reviewsAverageScore: number, reviewsCount: number) {
      this.reviewsAverageScore = reviewsAverageScore;
      this.reviewsCount = reviewsCount;
    },
    SET_PROMOTION (cartRibbon: CartRibbonData, ribbon: RibbonData, countdown: CountdownData, extraData: JSON) {
      this.cartRibbon = cartRibbon;
      this.ribbon = ribbon;
      this.countdown = countdown;
      this.extraData = extraData;
    },
    SET_REGION (regionData: RegionApiData) {
      this.oldSofaCollectionAvailable = regionData.oldSofaCollectionAvailable;
      this.countryLocale = regionData.countryLocale;
      this.regionName = regionData.regionName;
      this.regionCode = regionData.regionCode.toUpperCase();
      this.currencyCode = regionData.currencyCode;

      this.countryLocale = regionData.countryLocale;
      this.availableLanguages = regionData.availableLanguages || [];

      this.t03Available = regionData.t03Available;
      this.s01Available = regionData.s01Available;
      this.corduroyAvailable = regionData.corduroyAvailable;
      this.assemblyAvailable = regionData.assemblyAvailable;

      this.newsletterVoucherValue = regionData.newsletterVoucherValue;
      this.samplePrice = regionData.samplePrice;
      this.samplePromoPrice = regionData.samplePromoPrice;
      this.featureFlagsList = regionData.featureFlags;
    },
    SET_SAMPLES (samplesData: SamplesData) {
      this.samplesData = samplesData;
    },
    UPDATE_REGION ({
      currency,
      locale,
      region,
      region_code
    }: { currency: string; locale: string; region: RegionName; region_code: string }) {
      this.currencyCode = currency;
      this.countryLocale = locale;
      this.regionName = region;
      this.regionCode = region_code;
    },
    UPDATE_LIBRARY_ITEMS_COUNT (count: number) {
      this.libraryItemsCount = count;
    },
    UPDATE_CART_ITEMS_COUNT_BY_ONE () {
      this.cartItemsCount = (this.cartItemsCount ?? 0) + 1;
    }
  }
});
