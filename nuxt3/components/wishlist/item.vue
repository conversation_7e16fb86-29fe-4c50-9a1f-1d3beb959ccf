<template>
  <div class="relative overflow-hidden wishlist-item rounded-6 lg:flex mb-24">
    <BaseLink
      variant="custom"
      class="lg:w-1/2 xl:w-1/3 bg-[#e3e6e6] flex justify-center"
      data-testid="wishlist-item-image-link"
      v-bind="{
        href: url,
        trackData: {}
      }"
    >
      <img
        class="h-full object-cover max-h-[calc(100vw-32px)]"
        data-testid="wishlist-item-image"
        v-bind:src="preview"
        v-bind:alt="title"
      >
    </BaseLink>
    <div class="relative p-16 lg:w-1/2 xl:w-2/3">
      <BaseLink
        variant="custom"
        data-testid="wishlist-item-link"
        v-bind="{
          href: url,
          trackData: {}
        }"
      >
        <h3
          class="normal-16 lg:normal-24 lg:mb-16"
          v-html="itemName"
        />
      </BaseLink>

      <CheckoutPriceWrapper
        v-bind="{
          promotion,
          itemPriceWithoutDiscountRegionalizedNumber,
          itemPriceRegionalizedNumber
        }"
      />
      <div class="flex flex-col items-start gap-16">
        <p
          v-if="omnibusPrice"
          class="normal-12 text-[#6C6D70] my-4"
          v-html="`${$t('common.omnibusnotice')} ${omnibusPriceFormatted}`"
        />
        <p
          class="normal-12 text-grey-900 lg:normal-14"
          v-html="`${$t('common.dimensions')}: ${$t('scart.item_dimensions', { width, depth, height })}`"
        />
        <BaseButton
          variant="outlined"
          data-testid="wishlist-add-to-cart"
          v-bind="{
            disabled: isTO3WarningVisible || isS01WarningVisible || isCorduroyWarningVisible,
            trackData: {}
          }"
          v-on="{ click: addToCart }"
        >
          {{ $t('common.configurator.add_to_cart') }}
        </BaseButton>
        <p
          v-if="isTO3WarningVisible"
          class="normal-12 mt-4 text-grey-900"
        >
          The Tone Wardrobe is not available in this region.
        </p>
        <p
          v-if="isS01WarningVisible"
          class="normal-12 mt-4 text-grey-900"
        >
          {{ $t('pdp.sofa_availability.not_available_in_region') }}
        </p>
        <p
          v-if="isCorduroyWarningVisible"
          class="normal-12 mt-4 text-grey-900"
        >
          {{ $t('pdp.sofa_availability.corduroy_not_available') }}
        </p>
      </div>

      <BaseButton
        variant="link-color"
        class="box-content absolute p-8 top-8 right-8 group"
        data-testid="wishlist-bin-button"
        v-bind="{ trackData: {} }"
        v-on="{ click: showRemoveModal }"
      >
        <IconBin
          class="text-grey-900 transition-transform basic-transition transform group-hover:rotate-[20deg] group-focus:rotate-[20deg]"
        />
      </BaseButton>
    </div>
    <Transition name="fade">
      <WishlistRemoveItemModal
        v-if="showRemoveWishlistItemModal"
        v-on:hide-remove-modal="hideRemoveModal"
        v-on:handle-remove-item="removeItem"
      />
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toastification';
import { CREATE_CART } from '~/api/cart';
import { CART_ITEM_NAME } from '~/composables/useCart';
import { ADD_ITEM_TO_CART, REMOVE_WISHLIST_ITEM } from '~/api/wishlist';
import { CONFIGURATOR_TYPE_TO_MODEL } from '~/utils/configuratorTypeToModel';

import usePrice from '~/composables/usePrice';
import useCartStatus from '~/composables/useCartStatus';
import { SofaFinishType } from '~/consts/shelfType10';

const props = defineProps({
  preview: {
    type: String,
    required: true
  },
  promotion: {
    type: Number,
    required: true
  },
  itemPriceWithoutDiscountRegionalizedNumber: {
    type: Number,
    required: true
  },
  itemPriceRegionalizedNumber: {
    type: Number,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  },
  id: {
    type: [Number, String],
    required: true
  },
  configuratorType: {
    type: Number,
    required: true
  },
  width: {
    type: Number,
    required: true
  },
  depth: {
    type: Number,
    required: true
  },
  height: {
    type: Number,
    required: true
  },
  shelfType: {
    type: Number,
    required: true
  },
  omnibusPrice: {
    type: [Number, Boolean],
    default: false
  },
  category: {
    type: String,
    required: true
  },
  fabric: {
    type: String,
    default: ''
  }
});

const emit = defineEmits<{
  'updateFurnitureList': [data: any],
  'getEcommerceProductAdd': [id: number | string],
  'getEcommerceProductRemove': [id: number | string]
}>();

const { t } = useI18n();
const { format } = usePrice();
const { $logException } = useNuxtApp();
const { getItemName } = CART_ITEM_NAME();
const { openSidebarCart } = useCartStatus();
const { setHeaderDefaultState } = useHeader();

const toast = useToast();
const globalStore = useGlobal();
const { currencyCode, cartId, countryLocale, cartItemsCount } = storeToRefs(useGlobal());

const itemName = computed(() => getItemName(CONFIGURATOR_TYPE_TO_MODEL(props.configuratorType), '', props.category, props.shelfType));
const omnibusPriceFormatted = computed(() => format(
  typeof props.omnibusPrice === 'number' ? props.omnibusPrice : null,
  currencyCode.value,
  countryLocale.value
));

const modelType = computed(() => CONFIGURATOR_TYPE_TO_MODEL(props.configuratorType) as TYPE_MODEL);

const showRemoveWishlistItemModal = ref(false);

const showRemoveModal = () => {
  showRemoveWishlistItemModal.value = true;
};

const hideRemoveModal = () => {
  showRemoveWishlistItemModal.value = false;
};

const addToCart = async () => {
  try {
    if (!cartId.value) {
      const data = await CREATE_CART();

      cartId.value = data.data.value.id;
    }

    await ADD_ITEM_TO_CART(props.id, modelType.value, cartId.value as string | number);

    globalStore.UPDATE_CART_ITEMS_COUNT_BY_ONE();

    emit('getEcommerceProductAdd', props.id);

    openSidebarCart();
    setHeaderDefaultState();
  } catch (e: any) {
    if (e.response.status === 400 && cartItemsCount.value === 100) {
      toast.error(t('cart_max_size'));
    }

    $logException(e);
  }
};

const removeItem = async () => {
  try {
    emit('getEcommerceProductRemove', props.id);

    const data = await REMOVE_WISHLIST_ITEM(props.id, modelType.value);

    showRemoveWishlistItemModal.value = false;

    emit('updateFurnitureList', data.data.value);
  } catch (e) {
    $logException(e);
    toast.error(t('common.error.connection'));
  }
};

const isSofa = computed(() => props.shelfType === 10);
const isT03 = computed(() => props.shelfType === 3);
const isTO3WarningVisible = computed(() => isT03.value && !globalStore.t03Available);
const isS01WarningVisible = computed(() => isSofa.value && !globalStore.s01Available);
const isCorduroyWarningVisible = computed(() => isSofa.value && props.fabric === SofaFinishType.CORDUROY && !globalStore.corduroyAvailable);

</script>

<style lang="scss">
.wishlist-item {
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.05);
}
</style>
