<template>
  <BaseCarousel
    data-section="grid-v3-navigation"
    v-bind:class="[
      IS_SMOOTH ? 'plp-categories-with-sofas-swiper' : 'plp-categories-swiper',
      { 'pointer-events-none' : isPendingState }
    ]"
    class="pt-24 pb-32 lg:pt-32"
    v-bind="{
      isNavigation: false,
      name: `categoryListCarousel`,
      options: swiperOptions,
      swiperRootClasses: '!w-full plp-categories-carousel',
      navPrevElClasses: ['md:!left-16 plp-categories-nav-button', !isSwiperInitialized && 'swiper-button-disabled'],
      navNextElClasses: ['md:!right-16 plp-categories-nav-button', !isSwiperInitialized && 'swiper-button-disabled']
    }"
  >
    <BaseCarouselSlide
      v-for="category in categoriesWithPromoLabels"
      v-bind:key="category.name"
      v-bind:class="IS_SMOOTH ? 'plp-categories-with-sofas-carousel-item' : 'plp-categories-carousel-item'"
    >
      <NuxtLink
        class="relative flex flex-col justify-between items-center group box-content"
        v-bind:to="{ params: { category: $t(category.urlPathKey).replace('/', '') } }"
        v-bind:class="category.name === activeCategory.name ? 'pointer-events-none' : ''"
      >
        <BaseBadge
          v-if="category.promoLabel && extraData?.categoryBadges"
          variant="custom"
          class="absolute top-0 right-0 z-1 custom-color custom-bg-color"
        >
          {{ category.promoLabel }}
        </BaseBadge>
        <BaseBadge
          v-else-if="category.labelKey"
          variant="attribute"
          class="absolute top-0 right-0 z-1"
        >
          <span v-html="$t(category.labelKey)" />
        </BaseBadge>
        <BasePicture
          class="w-full aspect-square"
          img-classes="w-full aspect-square"
          type="A"
          v-bind="{
            path: `common/menu/categories/square/${category.name}`,
            alt: $t(category.pluralNameKey || category.nameKey) ,
            isRetinaUploaded: false
          }"
          disable-lazy
          disable-placeholder
        />
        <h3
          class="md-max:mt-12 flex items-center justify-center h-full min-h-24 max-h-36 text-center pb-2 normal-12 lg:normal-14 text-neutral-900
          border-neutral-900 transition-all group-hover:text-neutral-800"
          v-bind:class="category.name === activeCategory.name ? 'border-b semibold-12 lg:semibold-14' : 'border-transparent'"
          v-html="$t(category.gridNameKey || category.pluralNameKey || category.nameKey)"
        />
      </NuxtLink>
    </BaseCarouselSlide>
  </BaseCarousel>
</template>

<script setup lang="ts">
import type { Swiper } from 'swiper';
import type { SwiperOptions } from 'swiper/types';
import type { Category } from '~/consts/categories';

const props = withDefaults(defineProps<{
  activeCategory: Category,
  categoriesInPromotion?: Array<{
    categoryName: string,
    value: string
  }>,
  imgDirPath: string,
  isPendingState?: boolean
}>(), {
  categoriesInPromotion: () => [],
  isPendingState: false
});

const { extraData, IS_SMOOTH } = storeToRefs(useGlobal());
const { pickCategoriesInOrder } = useCategories();

const categories = pickCategoriesInOrder(
  IS_SMOOTH.value
    ? ['all', 'sofas', 'sideboard', 'bookcase', 'wallstorage', 'wardrobe', 'dressing_table', 'bedsideTable', 'tvstand', 'chest', 'shoerack', 'vinyl storage', 'desk']
    : ['all', 'sideboard', 'bookcase', 'wallstorage', 'wardrobe', 'dressing_table', 'bedsideTable', 'tvstand', 'chest', 'shoerack', 'vinyl storage', 'desk']
);

const activeCategoryIndex = computed(() => categories.findIndex(category => category.name === props.activeCategory.name) || 0);

const isSwiperInitialized = ref(false);

const swiperOptions: SwiperOptions = {
  slidesPerView: 'auto',
  slidesPerGroupAuto: true,
  centeredSlides: true,
  centeredSlidesBounds: true,
  centerInsufficientSlides: true,
  slideToClickedSlide: true,
  shortSwipes: false,
  freeMode: true,
  threshold: 4,
  on: {
    afterInit: (swiper: Swiper) => {
      isSwiperInitialized.value = true;

      // Hack to avoid swiper bug when it doesn't center slides properly on init if they are on the edges of swiper-wrapper
      setTimeout(() => {
        swiper.slideTo(activeCategoryIndex.value, activeCategoryIndex.value * 50);
      }, 100);
    },
    resize: swiper => swiper?.slideTo(activeCategoryIndex.value, 0)
  }
};

const categoriesWithPromoLabels = categories.map(el => ({
  ...el,
  promoLabel: props.categoriesInPromotion.find(item => item.categoryName === el.trackDataEventLabel || item.categoryName === el.name)?.value
}));

const badgeTextColor = computed(() => extraData.value?.textColorcustomBadgeTextColor);
const badgeBackgroundColor = computed(() => extraData.value?.customBadgeBackgroundColor);
</script>

<style lang="scss" scoped>
$category-item-width: 112px;
$categories-count: 12;
$categories-gap: 16px;
$categories-grid-padding: 96px;
$categories-swiper-disable-breakpoint: $category-item-width * $categories-count + $categories-gap * ($categories-count - 1) + $categories-grid-padding;

$categories-with-sofas-count: 13;
$category-with-sofas-item-width: 102px;
$categories-with-sofas-swiper-disable-breakpoint: $category-with-sofas-item-width * $categories-with-sofas-count + $categories-gap * ($categories-with-sofas-count - 1) + $categories-grid-padding;

.plp-categories-swiper:deep(.plp-categories-carousel) {
  @apply max-w-[1632px] mx-auto px-16 md:px-32 lg:px-48 xl:px-56 relative;

  .swiper-wrapper {
    @media screen and (min-width: $categories-swiper-disable-breakpoint) {
      transform: translateX(0) !important;

      @apply w-full;
    }
  }
}

.plp-categories-with-sofas-swiper:deep(.plp-categories-carousel) {
  @apply max-w-[1632px] mx-auto px-16 md:px-32 lg:px-48 xl:px-56 relative;

  .swiper-wrapper {
    @media screen and (min-width: $categories-with-sofas-swiper-disable-breakpoint) {
      transform: translateX(0) !important;

      @apply w-full;
    }
  }
}

.plp-categories-swiper:deep(.plp-categories-nav-button) {
  display: none !important;
}

.plp-categories-carousel-item {
  @apply w-[88px] mr-16 last:mr-0 md:w-[112px];
}
.plp-categories-with-sofas-carousel-item {
  @apply w-[88px] mr-16 last:mr-0 md:w-[102px];
}

.custom-color {
  color: v-bind(badgeTextColor);
}

.custom-bg-color {
  background-color: v-bind(badgeBackgroundColor);
}
</style>
