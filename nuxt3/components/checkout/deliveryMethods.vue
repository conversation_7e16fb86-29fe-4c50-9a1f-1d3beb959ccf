<template>
  <div>
    <div
      class="assembly-item mb-16"
      v-bind:class="{ active: cartUsedAssembly }"
      v-on:click="cartUsedAssembly = true"
    >
      <div class="flex items-center justify-center gap-x-16">
        <div class="w-full">
          <p
            class="bg-neutral-900 text-white inline px-8 py-2 rounded-4 normal-12"
            v-bind:class="{
              'bg-neutral-900': !assemblyPromo,
              'bg-success-500': assemblyPromo
            }"
          >
            {{ $t(assemblyPromo ? 'Promotion' : 'checkout.recommended_label') }}
          </p>
          <p class="mt-8 semibold-16 text-neutral-900">
            {{ $t('White gloves delivery & Assembly') }}
          </p>
          <p class="mt-2 text-neutral-700 normal-12">
            {{ $t('Our professional team will unpack and assemble your new sofa in the chosen room. They will take away all the packaging, leaving the room as they found it.') }}
          </p>
        </div>
        <p
          v-if="!assemblyPromo"
          class="normal-16 text-neutral-900"
        >
          {{ format(cart.orderPricing?.assembly_price) }}
        </p>
        <div
          v-else
          class="flex"
        >
          <p
            class="normal-16 text-neutral-700 mr-4 relative"
          >
            {{ format(cart.orderPricing?.assembly_price) }}
            <span
              class="absolute left-0 right-0 top-1/2 h-[1px] bg-neutral-700"
              style="content: ''; transform: translateY(-50%);"
            />
          </p>
          <p
            class="normal-16 text-orange"
          >
            {{ format(assemblyPromoPrice) }}
          </p>
        </div>
      </div>
    </div>
    <div
      class="assembly-item"
      v-bind:class="{ active: !cartUsedAssembly }"
      v-on:click="cartUsedAssembly = false"
    >
      <div class="flex items-center justify-center ">
        <div class="w-full">
          <p
            v-if="deliveryPromo"
            class="bg-success-500 text-white inline px-8 py-2 rounded-4 normal-12"
          >
            {{ $t('Promotion') }}
          </p>
          <p
            class="mt-8 semibold-16 text-neutral-900"
          >
            {{ $t('checkout.delivery.home_headline') }}
          </p>
          <p
            class="mt-2 text-neutral-700 normal-12"
          >
            {{ $t('Delivery right to your room.') }}
          </p>
        </div>
        <p
          v-if="!deliveryPromo"
          class="normal-16 text-neutral-900"
        >
          {{ format(deliveryPrice) }}
        </p>
        <div
          v-else
          class="flex"
        >
          <p
            class="normal-16 text-neutral-700 mr-4 relative"
          >
            {{ format(deliveryPrice) }}
            <span
              class="absolute left-0 right-0 top-1/2 h-[1px] bg-neutral-700"
              style="content: ''; transform: translateY(-50%);"
            />
          </p>
          <p
            class="normal-16 text-orange"
          >
            {{ format(deliveryPromoPrice) }}
          </p>
        </div>
      </div>
      <template
        v-for="(item, index) in cartItems"
        v-bind:key="index"
      >
        <div
          class="flex items-center justify-center mt-24"
        >
          <div class="w-full">
            <p
              class="normla-16 text-neutral-900"
            >
              {{ item.content_type === 'samplebox' ? item.itemDescriptionMaterial : getItemName(item) }}
            </p>
          </div>
          <p
            v-if="!item.deliveryPromo"
            class="normal-16 text-neutral-900"
          >
            {{ format(item.regionDeliveryPrice) }}
          </p>
          <div
            v-else
            class="flex"
          >
            <p
              class="normal-16 text-neutral-700 mr-4 relative"
            >
              {{ format(item.regionDeliveryPrice) }}
              <span
                class="absolute left-0 right-0 top-1/2 h-[1px] bg-neutral-700"
                style="content: ''; transform: translateY(-50%);"
              />
            </p>
            <p
              class="normal-16 text-orange"
            >
              {{ format(item.regionDeliveryPromoPrice) }}
            </p>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { checkoutAnalytics } from '~/composables/checkout/checkoutAnalytics';
import useSofaCartInfo from '~/composables/useSofaCartInfo';
import { CART_ITEM_NAME } from '~/composables/useCart';

const cart = useScartStore();
const { assemblyPromo, assemblyPromoPrice, deliveryPromo, deliveryPrice, deliveryPromoPrice, cartItems } = storeToRefs(cart);
const { handleAssembly } = useCart();
const $gtm = useGtm();
const { checkout2025Event } = checkoutAnalytics();
const toBoolean = (value: string | boolean) => !!(value === 'true' || value === true);

const {
  cartUsedAssembly
} = storeToRefs(cart);

const { format } = usePrice();

watch(cartUsedAssembly, async (value, oldValue) => {
  // there is bug in formkit that it emits boolen value as string on initial which triggers this watch
  // so we need to check if value is actually changed
  const booleanValue = toBoolean(value);
  const booleanOldValue = toBoolean(oldValue);

  if (booleanValue !== booleanOldValue) {
    await handleAssembly(booleanValue);

    if (booleanValue) {
      $gtm.push({ ecommerce: null });
      $gtm.push(checkout2025Event('add_shipping_info', null, 'Home delivery'));
    }
  }
});

const getItemName = (item) => {
  if (item.itemFurnitureType === 'sotty') {
    const {
      sofaItemName
    } = useSofaCartInfo(item.material, item.id);
    return sofaItemName;
  } else {
    const { getItemName } = CART_ITEM_NAME();
    return getItemName(item.itemFurnitureType, item.pattern_name, item.category, item.shelf_type);
  }
};

</script>

<style lang="scss" scoped>
    .assembly-item {
        @apply cursor-pointer rounded-8 border border-neutral-500 px-16 py-24;
        &.active {
            @apply border-neutral-900 bg-transparent relative overflow-hidden;
            &::before {
                    @apply absolute right-0 bottom-0 w-20 h-20 bg-neutral-900 bg-no-repeat rounded-tl-6;
                    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="1447.4 687.6 17.6 13.4"><g id="group" transform="translate(1421 687)"><path id="path" fill="%23FFFFFF" class="cls-1" d="M9,16.2,4.8,12,3.4,13.4,9,19,21,7,19.6,5.6Z" transform="translate(23 -5)"/></g></svg>');
                    background-size: 8px 8px;
                    background-position: 6px 6px;
                    content: '';
                }
        }
    }
</style>
